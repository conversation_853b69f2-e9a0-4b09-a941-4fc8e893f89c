package com.czb.hn.dto.alert;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Alert Result Response DTO
 * Contains complete alert result data for API responses
 * Data sourced from Elasticsearch processing
 */
public record AlertResultResponseDto(
        @Schema(description = "预警ID", example = "1") Long id,

        @Schema(description = "企业ID", example = "enterprise123") String enterpriseId,

        @Schema(description = "方案ID", example = "1") Long planId,

        @Schema(description = "配置ID", example = "1") Long configurationId,

        @Schema(description = "标题", example = "重要新闻标题") String title,

        @Schema(description = "正文", example = "新闻正文内容...") String content,

        @Schema(description = "原文链接", example = "http://example.com") String originUrl,

        @Schema(description = "涉及关键词", example = "[{\\\"六月\\\": 1}, {\\\"基金\\\": 4}]") String involvedKeywords,

        @Schema(description = "信息敏感性类型", example = "1", allowableValues = {
                "1", "2",
                "3" }) Integer informationSensitivityType,

        @Schema(description = "内容类别", example = "1", allowableValues = { "1",
                "2" }) Integer contentCategory,

        @Schema(description = "来源类型", example = "wb") String sourceType,

        @Schema(description = "内容类型", example = "1", allowableValues = { "1", "2", "3",
                "4" }) String contentType,

        @Schema(description = "内容匹配类型", example = "2") String contentMatchType,

        @Schema(description = "媒体级别", example = "央级") String mediaLevel,

        @Schema(description = "预警级别", example = "GENERAL") String warningLevel,

        @Schema(description = "来源", example = "微博") String source,

        @Schema(description = "省份", example = "北京") String provincial,

        @Schema(description = "发布时间", type = "string", format = "date-time", pattern = "yyyy-MM-dd HH:mm:ss", example = "2025-07-04 10:30:00") @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime publishTime,

        @Schema(description = "预警时间", type = "string", format = "date-time", pattern = "yyyy-MM-dd HH:mm:ss", example = "2025-07-04 10:30:00") @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime warningTime,

        @Schema(description = "相似文章数", example = "5") Integer similarArticleCount,

        @Schema(description = "原始内容ID", example = "content_123456") String originalContentId,

        @Schema(description = "扩展属性") Map<String, Object> extendedAttributes,

        @Schema(description = "创建时间", type = "string", format = "date-time", pattern = "yyyy-MM-dd HH:mm:ss", example = "2025-07-04 10:30:00") @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime createdAt,

        @Schema(description = "更新时间", type = "string", format = "date-time", pattern = "yyyy-MM-dd HH:mm:ss", example = "2025-07-04 14:30:00") @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime updatedAt){
}
