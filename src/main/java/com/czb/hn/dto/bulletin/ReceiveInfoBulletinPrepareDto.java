package com.czb.hn.dto.bulletin;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/07/12  11:10
 */
public record ReceiveInfoBulletinPrepareDto(
        @Schema(description = "是否开启邮件推送")
        Boolean emailEnable,
        @Schema(description = "邮件收件人ID列表")
        List<Long> emailRecipientIds,
        @Schema(description = "是否开启短信推送")
        Boolean smsEnable,
        @Schema(description = "短信收件人ID列表")
        List<Long> smsRecipientIds
) {
    // 紧凑的规范构造函数，用于验证输入参数
    public ReceiveInfoBulletinPrepareDto {
        // 如果启用了邮件但没有提供收件人ID，则抛出异常
        if (Boolean.TRUE.equals(emailEnable) && (emailRecipientIds == null || emailRecipientIds.isEmpty())) {
            throw new IllegalArgumentException("启用邮件推送时必须提供收件人ID列表");
        }
        // 如果启用了短信但没有提供收件人ID，则抛出异常
        if (Boolean.TRUE.equals(smsEnable) && (smsRecipientIds == null || smsRecipientIds.isEmpty())) {
            throw new IllegalArgumentException("启用短信推送时必须提供收件人ID列表");
        }
    }
}
