package com.czb.hn.dto.bulletin;

import com.czb.hn.dto.recipients.ReceiveInfoBulletinDto;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 简报接收信息准备响应DTO
 * 用于返回给前端显示收件人信息
 */
public record ReceiveInfoBulletinPrepareResponseDto(
        @Schema(description = "邮箱")
        ReceiveInfoBulletinDto emailReceiveInfoBulletinDto,
        @Schema(description = "短信")
        ReceiveInfoBulletinDto smsReceiveInfoBulletinDto
) {
} 