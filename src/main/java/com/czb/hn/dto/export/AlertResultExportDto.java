package com.czb.hn.dto.export;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.czb.hn.dto.alert.AlertResultResponseDto;
import com.czb.hn.enums.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 预警结果导出DTO
 * 专门用于Excel导出，所有枚举字段都转换为中文描述
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AlertResultExportDto {

    @ExcelProperty("序号")
    private Long id;

    @ExcelProperty("标题")
    private String title;

    @ExcelProperty("正文")
    private String content;

    @ExcelProperty("原文链接")
    private String originUrl;

    @ExcelProperty("涉及词及次数")
    private String involvedKeywords;

    @ExcelProperty("信息属性")
    private String informationSensitivityType;

    @ExcelProperty("内容类别")
    private String contentCategory;

    @ExcelProperty("预警级别")
    private String warningLevel;

    @ExcelProperty("来源类型")
    private String sourceType;

    @ExcelProperty("来源网站")
    private String sourceWebsite;

    @ExcelProperty("发布时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private LocalDateTime pushTime;

    /**
     * 从AlertResultResponseDto转换为导出DTO
     * 将所有枚举值转换为中文描述
     */
    public static AlertResultExportDto fromResponseDto(AlertResultResponseDto responseDto, int sequence) {
        AlertResultExportDto exportDto = new AlertResultExportDto();

        exportDto.setId((long) sequence);
        exportDto.setTitle(responseDto.title());
        exportDto.setContent(extractContentAroundKeywords(responseDto.content(), responseDto.involvedKeywords()));
        exportDto.setInvolvedKeywords(responseDto.involvedKeywords());
        exportDto.setOriginUrl(responseDto.originUrl());

        // 转换枚举值为中文描述
        exportDto.setInformationSensitivityType(
                convertInformationSensitivityType(responseDto.informationSensitivityType()));
        exportDto.setContentCategory(convertContentCategory(responseDto.contentCategory()));

        exportDto.setWarningLevel(convertWarningLevel(responseDto.warningLevel()));

        exportDto.setSourceType(convertSourceType(responseDto.sourceType()));
        exportDto.setSourceWebsite(responseDto.source());
        exportDto.setPushTime(responseDto.publishTime());

        return exportDto;
    }

    /**
     * 转换信息敏感性类型
     */
    private static String convertInformationSensitivityType(Integer value) {
        if (value == null) {
            return "";
        }
        for (InformationSensitivityType type : InformationSensitivityType.values()) {
            if (type.getValue().equals(value)) {
                return type.getDescription();
            }
        }
        return value.toString();
    }

    /**
     * 转换内容类别
     */
    private static String convertContentCategory(Integer value) {
        if (value == null) {
            return "";
        }
        for (ContentCategory category : ContentCategory.values()) {
            if (category.getValue().equals(value)) {
                return category.getDescription();
            }
        }
        return value.toString();
    }

    /**
     * 转换来源类型
     */
    private static String convertSourceType(String value) {
        if (value == null || value.isEmpty()) {
            return "";
        }
        return SourceType.fromString(value).getDescription();
    }

    /**
     * 转换内容类型
     */
    private static String convertContentType(String value) {
        if (value == null || value.isEmpty()) {
            return "";
        }
        for (ContentType type : ContentType.values()) {
            if (type.getValue().equals(value)) {
                return type.getDescription();
            }
        }
        return value;
    }

    /**
     * 转换内容匹配类型
     */
    private static String convertContentMatchType(String value) {
        if (value == null || value.isEmpty()) {
            return "";
        }
        for (ContentMatchType type : ContentMatchType.values()) {
            if (type.getValue().equals(value)) {
                return type.getDescription();
            }
        }
        return value;
    }

    /**
     * 转换预警级别
     */
    private static String convertWarningLevel(String value) {
        if (value == null || value.isEmpty()) {
            return "--";
        }
        // 直接映射预警级别
        switch (value.toUpperCase()) {
            case "SEVERE":
                return "严重";
            case "MEDIUM":
                return "中等";
            case "GENERAL":
                return "一般";
            default:
                return value;
        }
    }

    /**
     * 提取关键词附近的内容，最大150个字符
     *
     * @param content              原始内容
     * @param involvedKeywordsJson 涉及关键词的JSON字符串
     * @return 提取的内容片段
     */
    private static String extractContentAroundKeywords(String content, String involvedKeywordsJson) {
        if (content == null || content.isEmpty()) {
            return "";
        }

        if (involvedKeywordsJson == null || involvedKeywordsJson.isEmpty()) {
            // 如果没有关键词，返回前150个字符
            return content.length() <= 150 ? content : content.substring(0, 150) + "...";
        }

        try {
            // 解析JSON获取关键词
            java.util.List<String> keywords = parseKeywordsFromJson(involvedKeywordsJson);

            if (keywords.isEmpty()) {
                return content.length() <= 150 ? content : content.substring(0, 150) + "...";
            }

            // 查找第一个关键词的位置
            int keywordPosition = -1;
            String foundKeyword = null;
            for (String keyword : keywords) {
                int pos = content.toLowerCase().indexOf(keyword.toLowerCase());
                if (pos != -1) {
                    keywordPosition = pos;
                    foundKeyword = keyword;
                    break;
                }
            }

            if (keywordPosition == -1 || foundKeyword == null) {
                // 如果没有找到关键词，返回前150个字符
                return content.length() <= 150 ? content : content.substring(0, 150) + "...";
            }

            // 计算提取范围，考虑省略号的长度
            int maxLength = 150;
            int ellipsisLength = 3; // "..."的长度
            int availableLength = maxLength;

            // 预留省略号空间
            boolean needStartEllipsis = keywordPosition > 0;
            boolean needEndEllipsis = keywordPosition + foundKeyword.length() < content.length();

            if (needStartEllipsis)
                availableLength -= ellipsisLength;
            if (needEndEllipsis)
                availableLength -= ellipsisLength;

            int keywordLength = foundKeyword.length();
            int beforeLength = (availableLength - keywordLength) / 2;
            int afterLength = availableLength - keywordLength - beforeLength;

            int startPos = Math.max(0, keywordPosition - beforeLength);
            int endPos = Math.min(content.length(), keywordPosition + keywordLength + afterLength);

            String extracted = content.substring(startPos, endPos);

            // 添加省略号
            if (startPos > 0) {
                extracted = "..." + extracted;
            }
            if (endPos < content.length()) {
                extracted = extracted + "...";
            }

            return extracted;

        } catch (Exception e) {
            // 解析失败时返回前150个字符
            return content.length() <= 150 ? content : content.substring(0, 150) + "...";
        }
    }

    /**
     * 从JSON字符串中解析关键词列表
     *
     * @param involvedKeywordsJson JSON字符串，格式如：[{"keyword":"关键词1","count":3}] 或
     *                             {"keyword":"关键词1","count":3}
     * @return 关键词列表
     */
    private static java.util.List<String> parseKeywordsFromJson(String involvedKeywordsJson) {
        java.util.List<String> keywords = new java.util.ArrayList<>();

        try {
            // 简单的JSON解析，提取keyword字段的值
            String json = involvedKeywordsJson.trim();

            if (json.startsWith("[") && json.endsWith("]")) {
                // 数组格式
                json = json.substring(1, json.length() - 1); // 移除[]
                String[] objects = json.split("\\},\\s*\\{");

                for (String obj : objects) {
                    obj = obj.replaceAll("[\\[\\]{}]", ""); // 清理括号
                    String keyword = extractKeywordFromObject(obj);
                    if (keyword != null && !keyword.isEmpty()) {
                        keywords.add(keyword);
                    }
                }
            } else if (json.startsWith("{") && json.endsWith("}")) {
                // 单个对象格式
                json = json.substring(1, json.length() - 1); // 移除{}
                String keyword = extractKeywordFromObject(json);
                if (keyword != null && !keyword.isEmpty()) {
                    keywords.add(keyword);
                }
            }

        } catch (Exception e) {
            // 解析失败，返回空列表
        }

        return keywords;
    }

    /**
     * 从JSON对象字符串中提取keyword字段的值
     */
    private static String extractKeywordFromObject(String objectStr) {
        try {
            // 查找"keyword":"value"模式
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("\"keyword\"\\s*:\\s*\"([^\"]+)\"");
            java.util.regex.Matcher matcher = pattern.matcher(objectStr);

            if (matcher.find()) {
                return matcher.group(1);
            }
        } catch (Exception e) {
            // 解析失败
        }

        return null;
    }
}