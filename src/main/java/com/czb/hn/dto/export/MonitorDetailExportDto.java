package com.czb.hn.dto.export;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.czb.hn.dto.response.search.SinaNewsDetailResponseDto;
import com.czb.hn.enums.InformationSensitivityType;
import com.czb.hn.enums.SourceType;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 监控详情导出DTO
 * 专门用于Excel导出，所有枚举字段都转换为中文描述
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MonitorDetailExportDto {
    @ExcelProperty("序号")
    private Integer sequence;

    @ExcelProperty("标题")
    private String title;

    @ExcelProperty("内容")
    private String content;

    @ExcelProperty("原文链接")
    private String url;

    @ExcelProperty("涉及词")
    private String highLightWords;

    @ExcelProperty("信息属性")
    private String sensitivityType;

    @ExcelProperty("相似文章数")
    private Long similarityNum;

    @ExcelProperty("来源类型")
    private String sourceType;

    @ExcelProperty("来源网站")
    private String captureWebsite;

    @ExcelProperty("发布时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private LocalDateTime publishTime;



    /**
     * 从SinaNewsDetailResponseDto转换为导出DTO
     * 将所有枚举值转换为中文描述
     */
    public static MonitorDetailExportDto fromResponseDto(SinaNewsDetailResponseDto responseDto, int sequence) {
        MonitorDetailExportDto exportDto = new MonitorDetailExportDto();

        exportDto.setSequence(sequence);
        exportDto.setTitle(responseDto.getTitle());
        exportDto.setContent(
                extractContentAroundHighlightWords(responseDto.getHighlightContent(), responseDto.getHighLightWords()));
        exportDto.setUrl(responseDto.getUrl());
        exportDto.setHighLightWords(convertHighLightWords(responseDto.getHighLightWords()));
        exportDto.setSensitivityType(convertSensitivityType(responseDto.getSensitivityType()));
        exportDto.setSimilarityNum(responseDto.getSimilarityNum());

        exportDto.setSourceType(convertMidiaType(responseDto.getMediaType()));
        exportDto.setCaptureWebsite(responseDto.getCaptureWebsite());

        exportDto.setPublishTime(responseDto.getPublishTime());
        return exportDto;
    }

    private static String convertMidiaType(String mediaType) {
        if (mediaType == null || mediaType.isEmpty()) {
            return "";
        }
        return SourceType.fromString(mediaType).getDescription();
    }

    /**
     * 提取涉及词附近的内容，最大150个字符
     *
     * @param highlightContent 高亮内容Map
     * @param highLightWords   涉及词列表
     * @return 提取的内容片段
     */
    private static String extractContentAroundHighlightWords(Map<String, List<String>> highlightContent,
            List<Map<String, Integer>> highLightWords) {
        if (highlightContent == null || highlightContent.isEmpty()) {
            return "";
        }

        // 获取原始内容
        String content = highlightContent.entrySet().stream()
                .map(entry -> String.join(" ", entry.getValue()))
                .collect(Collectors.joining(" "));

        if (content.isEmpty()) {
            return "";
        }

        // 获取涉及词列表
        List<String> keywords = extractKeywordsFromHighLightWords(highLightWords);

        if (keywords.isEmpty()) {
            // 如果没有关键词，返回前150个字符
            return content.length() <= 150 ? content : content.substring(0, 150) + "...";
        }

        // 查找第一个关键词的位置
        int keywordPosition = -1;
        String foundKeyword = null;
        for (String keyword : keywords) {
            int pos = content.toLowerCase().indexOf(keyword.toLowerCase());
            if (pos != -1) {
                keywordPosition = pos;
                foundKeyword = keyword;
                break;
            }
        }

        if (keywordPosition == -1 || foundKeyword == null) {
            // 如果没有找到关键词，返回前150个字符
            return content.length() <= 150 ? content : content.substring(0, 150) + "...";
        }

        // 计算提取范围，考虑省略号的长度
        int maxLength = 150;
        int ellipsisLength = 3; // "..."的长度
        int availableLength = maxLength;

        // 预留省略号空间
        boolean needStartEllipsis = keywordPosition > 0;
        boolean needEndEllipsis = keywordPosition + foundKeyword.length() < content.length();

        if (needStartEllipsis)
            availableLength -= ellipsisLength;
        if (needEndEllipsis)
            availableLength -= ellipsisLength;

        int keywordLength = foundKeyword.length();
        int beforeLength = (availableLength - keywordLength) / 2;
        int afterLength = availableLength - keywordLength - beforeLength;

        int startPos = Math.max(0, keywordPosition - beforeLength);
        int endPos = Math.min(content.length(), keywordPosition + keywordLength + afterLength);

        String extracted = content.substring(startPos, endPos);

        // 添加省略号
        if (startPos > 0) {
            extracted = "..." + extracted;
        }
        if (endPos < content.length()) {
            extracted = extracted + "...";
        }

        return extracted;
    }

    /**
     * 从涉及词列表中提取关键词
     */
    private static List<String> extractKeywordsFromHighLightWords(List<Map<String, Integer>> highLightWords) {
        List<String> keywords = new java.util.ArrayList<>();

        if (highLightWords != null) {
            for (Map<String, Integer> wordMap : highLightWords) {
                keywords.addAll(wordMap.keySet());
            }
        }

        return keywords;
    }

    /**
     * 转换信息敏感性类型
     */
    private static String convertSensitivityType(Integer value) {
        if (value == null) {
            return "";
        }
        for (InformationSensitivityType type : InformationSensitivityType.values()) {
            if (type.getValue().equals(value)) {
                return type.getDescription();
            }
        }
        return value.toString();
    }

    /**
     * 转换涉及词列表为字符串
     */
    private static String convertHighLightWords(List<Map<String, Integer>> highLightWords) {
        if (highLightWords == null || highLightWords.isEmpty()) {
            return "";
        }

        return highLightWords.stream()
                .map(wordMap -> wordMap.entrySet().stream()
                        .map(entry -> entry.getKey() + "(" + entry.getValue() + ")")
                        .collect(Collectors.joining(", ")))
                .collect(Collectors.joining("; "));
    }
}
