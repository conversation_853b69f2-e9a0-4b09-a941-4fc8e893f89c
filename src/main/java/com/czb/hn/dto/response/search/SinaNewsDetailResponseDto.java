package com.czb.hn.dto.response.search;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 新浪舆情信息详情结果DTO
 * 用于控制器返回数据，避免直接暴露ES文档结构
 */
@Data
@NoArgsConstructor
@Schema(description = "新浪舆情数据详情结果对象")
@JsonIgnoreProperties(ignoreUnknown = true) //忽略所有不匹配字段
public class SinaNewsDetailResponseDto {
    @Schema(description = "内容ID")
    private String contentId;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "内容")
    private Map<String, List<String>> highlightContent;

    @Schema(description = "发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime publishTime;

    @Schema(description = "来源类型")
    private String mediaType;

    @Schema(description = "来源")
    private String captureWebsite;

    @Schema(description = "作者")
    private String author;

    @Schema(description = "信息属性")
    private Integer sensitivityType;

    @Schema(description = "涉及词")
    private List<Map<String,Integer>> highLightWords;

    @Schema(description = "相似文章数")
    private Long similarityNum;

    @Schema(description = "发布地")
    private String publishProvince;

    @Schema(description = "网页地址")
    private String url;
}
