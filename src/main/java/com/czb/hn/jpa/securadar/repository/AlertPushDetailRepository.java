package com.czb.hn.jpa.securadar.repository;

import com.czb.hn.jpa.securadar.entity.AlertPushDetail;
import com.czb.hn.enums.PushStatus;
import com.czb.hn.enums.PushType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Repository interface for AlertPushDetail entity
 * Provides data access methods for alert push detail management
 * with tenant isolation and performance optimization
 */
@Repository
public interface AlertPushDetailRepository extends JpaRepository<AlertPushDetail, Long> {

        /**
         * Find push details with complex filtering
         */
        @Query("SELECT apd FROM AlertPushDetail apd WHERE " +
                        "(:enterpriseId IS NULL OR apd.enterpriseId = :enterpriseId) AND " +
                        "(:planId IS NULL OR apd.planId = :planId) AND " +
                        "(:pushType IS NULL OR apd.pushType = :pushType) AND " +
                        "(:pushStatus IS NULL OR apd.pushStatus = :pushStatus) AND " +
                        "(:startTime IS NULL OR apd.pushTime >= :startTime) AND " +
                        "(:endTime IS NULL OR apd.pushTime <= :endTime) " +
                        "ORDER BY apd.pushTime DESC")
        Page<AlertPushDetail> findWithFilters(
                        @Param("enterpriseId") String enterpriseId,
                        @Param("planId") Long planId,
                        @Param("pushType") PushType pushType,
                        @Param("pushStatus") PushStatus pushStatus,
                        @Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime,
                        Pageable pageable);


        Page<AlertPushDetail> findByAlertNotificationQueueId(Long alertNotificationQueueId, Pageable pageable);

}
