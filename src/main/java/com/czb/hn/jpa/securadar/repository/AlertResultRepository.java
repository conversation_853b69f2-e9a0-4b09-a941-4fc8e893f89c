package com.czb.hn.jpa.securadar.repository;

import com.czb.hn.jpa.securadar.entity.AlertResult;
import com.czb.hn.enums.ContentCategory;
import com.czb.hn.enums.ContentType;
import com.czb.hn.enums.InformationSensitivityType;
import com.czb.hn.enums.MediaLevel;
import com.czb.hn.enums.SourceType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Repository interface for AlertResult entity
 * Provides data access methods with tenant isolation and performance
 * optimization
 */
@Repository
public interface AlertResultRepository extends JpaRepository<AlertResult, Long> {

        /**
         * Check if an alert result already exists for the given enterprise, content,
         * and configuration
         * to prevent duplicate alerts
         */
        boolean existsByEnterpriseIdAndOriginalContentIdAndConfigurationId(
                        String enterpriseId, String originalContentId, Long configurationId);

        @Query(value = "SELECT * FROM alert_results ar WHERE ar.plan_id = :planId " +
                        "AND (:informationSensitivityType IS NULL OR ar.information_sensitivity_type = :informationSensitivityType) "
                        +
                        "AND (:warningLevel IS NULL OR ar.warning_level = :warningLevel) " +
                        "AND (:sourceType IS NULL OR ar.source_type = :sourceType) " +
                        "AND (:startTime IS NULL OR ar.warning_time >= :startTime) " +
                        "AND (:endTime IS NULL OR ar.warning_time <= :endTime) " +
                        "AND (:searchText IS NULL OR MATCH(ar.title, ar.content) AGAINST(:searchText IN NATURAL LANGUAGE MODE) "
                        +
                        "OR ar.title LIKE CONCAT('%', :searchText, '%') OR ar.content LIKE CONCAT('%', :searchText, '%')) "
                        +
                        "ORDER BY ar.warning_time DESC", nativeQuery = true)
        Page<AlertResult> findWithFiltersAndFullTextSearch(
                        @Param("planId") Long planId,
                        @Param("informationSensitivityType") Integer informationSensitivityType,
                        @Param("warningLevel") String warningLevel,
                        @Param("sourceType") String sourceType,
                        @Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime,
                        @Param("searchText") String searchText,
                        Pageable pageable);

        /**
         * Check if alerts exist for configuration within time range
         */
        boolean existsByConfigurationIdAndWarningTimeBetween(
                        Long configurationId, LocalDateTime startTime, LocalDateTime endTime);

        /**
         * Count total alerts by plan ID within time range
         */
        @Query("SELECT COUNT(ar) FROM AlertResult ar WHERE ar.planId = :planId " +
                        "AND (:startTime IS NULL OR ar.warningTime >= :startTime) " +
                        "AND (:endTime IS NULL OR ar.warningTime <= :endTime)")
        Long countByPlanIdAndWarningTimeBetween(
                        @Param("planId") Long planId,
                        @Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime);

        /**
         * Count sensitive alerts by plan ID within time range
         * Note: SENSITIVE enum ordinal is 0 (first in enum definition)
         */
        @Query("SELECT COUNT(ar) FROM AlertResult ar WHERE ar.planId = :planId " +
                        "AND ar.informationSensitivityType = com.czb.hn.enums.InformationSensitivityType.SENSITIVE " +
                        "AND (:startTime IS NULL OR ar.warningTime >= :startTime) " +
                        "AND (:endTime IS NULL OR ar.warningTime <= :endTime)")
        Long countSensitiveByPlanIdAndWarningTimeBetween(
                        @Param("planId") Long planId,
                        @Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime);

        /**
         * Batch count alerts by plan IDs within time range
         * Returns results grouped by plan ID for performance optimization
         */
        @Query("SELECT ar.planId, COUNT(ar) FROM AlertResult ar WHERE ar.planId IN :planIds " +
                        "AND (:startTime IS NULL OR ar.warningTime >= :startTime) " +
                        "AND (:endTime IS NULL OR ar.warningTime <= :endTime) " +
                        "GROUP BY ar.planId")
        List<Object[]> batchCountByPlanIdsAndWarningTimeBetween(
                        @Param("planIds") List<Long> planIds,
                        @Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime);

        /**
         * Find top K alerts for a specific plan ordered by warning time descending
         *
         * @param planId the plan ID
         * @param limit  the maximum number of results to return
         * @return list of alert results ordered by warning time descending
         */
        @Query("SELECT ar FROM AlertResult ar WHERE ar.planId = :planId " +
                        "ORDER BY ar.warningTime DESC")
        List<AlertResult> findTopAlertsByPlanId(@Param("planId") Long planId, Pageable pageable);

        /**
         * Find top K alerts for an enterprise ordered by warning time descending
         *
         * @param enterpriseId the enterprise ID
         * @param limit        the maximum number of results to return
         * @return list of alert results ordered by warning time descending
         */
        @Query("SELECT ar FROM AlertResult ar WHERE ar.enterpriseId = :enterpriseId " +
                        "ORDER BY ar.warningTime DESC")
        List<AlertResult> findTopAlertsByEnterpriseId(@Param("enterpriseId") String enterpriseId, Pageable pageable);

        /**
         * Find top K alerts for a specific plan within time range ordered by warning
         * time descending
         *
         * @param planId    the plan ID
         * @param startTime start time (optional)
         * @param endTime   end time (optional)
         * @param pageable  pagination parameters
         * @return list of alert results ordered by warning time descending
         */
        @Query("SELECT ar FROM AlertResult ar WHERE ar.planId = :planId " +
                        "AND (:startTime IS NULL OR ar.warningTime >= :startTime) " +
                        "AND (:endTime IS NULL OR ar.warningTime <= :endTime) " +
                        "ORDER BY ar.warningTime DESC")
        List<AlertResult> findTopAlertsByPlanIdAndTimeRange(@Param("planId") Long planId,
                        @Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime,
                        Pageable pageable);

        /**
         * Find top K alerts for an enterprise within time range ordered by warning time
         * descending
         *
         * @param enterpriseId the enterprise ID
         * @param startTime    start time (optional)
         * @param endTime      end time (optional)
         * @param pageable     pagination parameters
         * @return list of alert results ordered by warning time descending
         */
        @Query("SELECT ar FROM AlertResult ar WHERE ar.enterpriseId = :enterpriseId " +
                        "AND (:startTime IS NULL OR ar.warningTime >= :startTime) " +
                        "AND (:endTime IS NULL OR ar.warningTime <= :endTime) " +
                        "ORDER BY ar.warningTime DESC")
        List<AlertResult> findTopAlertsByEnterpriseIdAndTimeRange(@Param("enterpriseId") String enterpriseId,
                        @Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime,
                        Pageable pageable);

        /**
         * Find top K alerts for multiple plans within time range ordered by warning
         * time
         * descending
         *
         * @param planIds   list of plan IDs
         * @param startTime start time (optional)
         * @param endTime   end time (optional)
         * @param pageable  pagination parameters
         * @return list of alert results ordered by warning time descending
         */
        @Query("SELECT ar FROM AlertResult ar WHERE ar.planId IN :planIds " +
                        "AND (:startTime IS NULL OR ar.warningTime >= :startTime) " +
                        "AND (:endTime IS NULL OR ar.warningTime <= :endTime) " +
                        "ORDER BY ar.warningTime DESC")
        List<AlertResult> findTopAlertsByPlanIdsAndTimeRange(@Param("planIds") List<Long> planIds,
                        @Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime,
                        Pageable pageable);

        /**
         * Find alerts associated with a notification queue
         */
        List<AlertResult> findByAlertNotificationQueueId(Long alertNotificationQueueId);


        /**
         * 查找指定配置和时间范围内未通知的预警
         */
        @Query("SELECT ar FROM AlertResult ar WHERE ar.configurationId = :configurationId " +
                        "AND ar.warningTime BETWEEN :startTime AND :endTime " +
                        "AND ar.alertNotificationQueueId IS NULL")
        List<AlertResult> findUnnotifiedAlertsByConfigurationAndTimeRange(
                        @Param("configurationId") Long configurationId,
                        @Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime);


        /**
         * 统计指定配置和时间范围内的预警数量
         */
        @Query("SELECT COUNT(ar) FROM AlertResult ar WHERE ar.configurationId = :configurationId " +
                        "AND ar.warningTime BETWEEN :startTime AND :endTime")
        long countAlertsByConfigurationAndTimeRange(
                        @Param("configurationId") Long configurationId,
                        @Param("startTime") LocalDateTime startTime,
                        @Param("endTime") LocalDateTime endTime);

}
