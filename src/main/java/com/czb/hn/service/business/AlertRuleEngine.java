package com.czb.hn.service.business;

import com.czb.hn.document.SinaNewsDocument;
import com.czb.hn.dto.alert.AlertConfigurationResponseDto;
import com.czb.hn.dto.alert.config.*;
import com.czb.hn.enums.ContentMatchType;
import com.czb.hn.jpa.securadar.entity.AlertConfiguration;
import com.czb.hn.jpa.securadar.entity.AlertResult;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * Alert Rule Engine
 * Evaluates JSON-based alert rules against Elasticsearch documents
 * Determines warning levels and processes complex rule conditions
 */
@Component
@Slf4j
public class AlertRuleEngine {

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * Evaluate alert rule against Elasticsearch document
     * 
     * @param config   Alert configuration with JSON rules
     * @param document Elasticsearch document (SinaNewsDocument)
     * @return true if alert should be triggered
     */
    public boolean evaluateRule(AlertConfiguration config, SinaNewsDocument document) {
        try {
            // Validate input parameters
            if (config == null || document == null) {
                log.warn("Cannot evaluate rule: config or document is null");
                return false;
            }

            // Parse JSON configurations
            AlertKeywordsDto keywords = parseAlertKeywords(config.getAlertKeywords());
            ContentSettingsDto contentSettings = parseContentSettings(config.getContentSettings());
            ThresholdSettingsDto thresholdSettings = parseThresholdSettings(config.getThresholdSettings());

            // Check if any required configuration failed to parse
            if (config.getAlertKeywords() != null && !config.getAlertKeywords().trim().isEmpty() && keywords == null) {
                log.warn("Alert keywords configuration is invalid for config {}", config.getId());
                return false;
            }

            // Evaluate each rule component with null safety
            boolean keywordMatch = evaluateKeywordRules(keywords, contentSettings, document);
            boolean contentMatch = evaluateContentRules(contentSettings, document);
            boolean thresholdMatch = evaluateThresholdRules(thresholdSettings, document);

            // Combine results based on configuration logic (AND operation)
            boolean result = keywordMatch && contentMatch && thresholdMatch;

            if (result) {
                log.debug("Alert rule triggered for document {} with configuration {}",
                        document.getContentId(), config.getId());
            }

            return result;

        } catch (Exception e) {
            log.error("Error evaluating rule for configuration {} against document {}: {}",
                    config != null ? config.getId() : "null",
                    document != null ? document.getContentId() : "null",
                    e.getMessage(), e);
            return false;
        }
    }


    /**
     * Evaluate content match type
     *
     * @param config
     *
     * @return ContentMatchType
     *
     */

    public ContentMatchType extractContentMatchType(AlertConfigurationResponseDto config) {
        Integer matchMethod = config.contentSettings().matchMethod();
        return ContentMatchType.fromInteger(matchMethod);
    }

    /**
     * Determine warning level based on document content and level settings
     * If levelSettings is null, returns GENERAL as default level
     *
     * @param levelSettings Level configuration settings (can be null)
     * @param document      Elasticsearch document
     * @return Determined warning level
     */
    public AlertResult.WarningLevel determineWarningLevel(
            LevelSettingsDto levelSettings, SinaNewsDocument document) {

        // If no level settings configured, return default level
        if (levelSettings == null) {
            log.debug("No level settings configured, using default GENERAL level for document {}",
                    document.getContentId());
            return null;
        }

        AlertResult.WarningLevel highestLevel = null;

        // 计算信源级别
        if (levelSettings.sourceLevelEnabled()) {
            Map<String, String> sourceLevelMap = levelSettings.sourceLevel();
            String mediaLevel = document.getMediaLevel();
            String warnLevelStr = sourceLevelMap.getOrDefault(mediaLevel, null);
            if (StringUtils.isNotBlank(warnLevelStr)) {
                AlertResult.WarningLevel sourceLevel = parseWarningLevel(warnLevelStr);
                highestLevel = getHigherLevel(highestLevel, sourceLevel);
                log.debug("Source level calculation for document {}: mediaLevel={}, warningLevel={}",
                        document.getContentId(), mediaLevel, sourceLevel);
            }
        }

        // 计算互动数
        if (levelSettings.interactionThresholds() != null && levelSettings.interactionThresholds().enabled()) {
            long totalInteractions = calculateTotalInteractions(document);
            AlertResult.WarningLevel interactionLevel = determineThresholdLevel(
                    levelSettings.interactionThresholds(), totalInteractions);
            highestLevel = getHigherLevel(highestLevel, interactionLevel);
            log.debug("Interaction level calculation for document {}: totalInteractions={}, warningLevel={}",
                    document.getContentId(), totalInteractions, interactionLevel);
        }

        // 计算粉丝数
        if (levelSettings.fansThresholds() != null && levelSettings.fansThresholds().enabled()) {
            long fansCount = document.getAuthorFollowersCount() != null ? document.getAuthorFollowersCount() : 0L;
            AlertResult.WarningLevel fansLevel = determineThresholdLevel(
                    levelSettings.fansThresholds(), fansCount);
            highestLevel = getHigherLevel(highestLevel, fansLevel);
            log.debug("Fans level calculation for document {}: fansCount={}, warningLevel={}",
                    document.getContentId(), fansCount, fansLevel);
        }

        // 计算阅读数
        if (levelSettings.readThresholds() != null && levelSettings.readThresholds().enabled()) {
            long readCount = document.getLookingCount() != null ? document.getLookingCount() : 0L;
            AlertResult.WarningLevel readLevel = determineThresholdLevel(
                    levelSettings.readThresholds(), readCount);
            highestLevel = getHigherLevel(highestLevel, readLevel);
            log.debug("Read level calculation for document {}: readCount={}, warningLevel={}",
                    document.getContentId(), readCount, readLevel);
        }

        // 计算相似文章数
        if (levelSettings.similarArticleThresholds() != null && levelSettings.similarArticleThresholds().enabled()) {
            long similarCount = document.getSimilarityNum() != null ? document.getSimilarityNum() : 0L;
            AlertResult.WarningLevel similarLevel = determineThresholdLevel(
                    levelSettings.similarArticleThresholds(), similarCount);
            highestLevel = getHigherLevel(highestLevel, similarLevel);
            log.debug("Similar article level calculation for document {}: similarCount={}, warningLevel={}",
                    document.getContentId(), similarCount, similarLevel);
        }

        log.debug("Final warning level for document {}: {}", document.getContentId(), highestLevel);
        return highestLevel;
    }

    /**
     * Evaluate keyword rules against document content
     * Uses simple OR logic - if ANY keyword matches, the rule triggers
     */
    private boolean evaluateKeywordRules(AlertKeywordsDto keywords, ContentSettingsDto contentSettings,
            SinaNewsDocument document) {
        // If no keywords configured, return true (no keyword filtering)
        if (keywords == null || keywords.keywords() == null || keywords.keywords().isEmpty()) {
            log.debug("No keywords configured, allowing all content for document {}",
                    document.getContentId());
            return true;
        }

        String content = "";
        if (contentSettings == null) {
            log.debug("No content settings configured, allowing all content for document {}", document.getContentId());

            if (StringUtils.isNotBlank(document.getTitle())) {
                content += document.getTitle();
            }

            if (StringUtils.isNotBlank(document.getContent())) {
                content += document.getContent();
            }
        } else {
            Integer matchMethod = contentSettings.matchMethod();
            ContentMatchType contentMatchType = ContentMatchType.fromInteger(matchMethod);
            if (ContentMatchType.TITLE.equals(contentMatchType)) {
                if (StringUtils.isNotBlank(document.getTitle())) {
                    content = document.getTitle();
                }
            } else if (ContentMatchType.MAIN_TXT.equals(contentMatchType)) {
                if (StringUtils.isNotBlank(document.getContent())) {
                    content = document.getContent();
                }
            } else {
                if (StringUtils.isNotBlank(document.getTitle())) {
                    content += document.getTitle();
                }
                if (StringUtils.isNotBlank(document.getContent())) {
                    content += document.getContent();
                }
            }
        }

        log.debug("Evaluating keywords {} against content: {}", keywords.keywords(), content);

        // Simple OR logic: if ANY keyword from the list matches the content, return
        // true
        for (String keyword : keywords.keywords()) {
            if (keyword != null && !keyword.trim().isEmpty()) {
                String trimmedKeyword = keyword.trim();
                if (content.toLowerCase().contains(trimmedKeyword.toLowerCase())) {
                    log.debug("Keyword '{}' matched in content for document {}",
                            trimmedKeyword, document.getContentId());
                    return true;
                }
            }
        }

        log.debug("No keywords matched for document {}", document.getContentId());
        return false;
    }

    /**
     * Evaluate content rules against document properties
     * If contentSettings is null, returns true (no filtering applied)
     */
    private boolean evaluateContentRules(ContentSettingsDto contentSettings, SinaNewsDocument document) {
        // If no content settings configured, allow all content
        if (contentSettings == null) {
            log.debug("No content settings configured, allowing all content for document {}",
                    document.getContentId());
            return true;
        }

        // Check sensitivity type
        boolean sensitivityRet = true;
        List<Integer> sensitivityTypes = contentSettings.sensitivityTypes();
        if (!CollectionUtils.isEmpty(sensitivityTypes)) {
            if (sensitivityTypes.stream().noneMatch(it -> it.equals(document.getSensitivityType()))) {
                sensitivityRet = false;
            }
        }

        boolean mediaTypeRet = true;
        List<String> mediaTypes = contentSettings.mediaTypes();
        if (!CollectionUtils.isEmpty(mediaTypes)) {
            mediaTypeRet = mediaTypes.stream().anyMatch(it -> it.equals(document.getMediaType()));
        }

        boolean contentTypeRet = true;
        List<Integer> contentTypes = contentSettings.contentTypes();
        if (!CollectionUtils.isEmpty(contentTypes)) {
            contentTypeRet = contentTypes.stream().anyMatch(it -> it.equals(document.getContentTypes()));
        }

        boolean originalRet = true;
        Integer original = contentSettings.isOriginal();
        if (original != null) {
            originalRet = original.equals(document.getIsOriginal());
        }

        boolean secondTradeRet = true;
        List<String> secondTrades = contentSettings.secondTrades();
        if (!CollectionUtils.isEmpty(secondTrades)) {
            // list之间存在交集即可
            secondTradeRet = document.getSecondTrades().stream()
                    .anyMatch(secondTrades::contains);
        }

        boolean mediaLevelsRet = true;
        List<String> mediaLevels = contentSettings.mediaLevels();
        if (!CollectionUtils.isEmpty(mediaLevels)) {
            mediaLevelsRet = mediaLevels.stream().anyMatch(it -> it.equals(document.getMediaLevel()));
        }

        return sensitivityRet && mediaTypeRet && contentTypeRet && originalRet && secondTradeRet && mediaLevelsRet;
    }

    /**
     * Evaluate threshold rules against document metrics
     * If thresholdSettings is null, returns true (no threshold filtering applied)
     */
    private boolean evaluateThresholdRules(ThresholdSettingsDto thresholdSettings, SinaNewsDocument document) {
        // If no threshold settings configured, allow all documents
        if (thresholdSettings == null) {
            log.debug("No threshold settings configured, allowing all documents for document {}",
                    document.getContentId());
            return true;
        }

        List<Boolean> conditionResults = new ArrayList<>();

        // Check interaction count threshold
        if (thresholdSettings.interactionCount() != null && thresholdSettings.interactionCount().enabled()) {
            long totalInteractions = calculateTotalInteractions(document);
            boolean meets = totalInteractions >= thresholdSettings.interactionCount().threshold();
            conditionResults.add(meets);
        }

        // Check fans count threshold (using authorFollowersCount)
        if (thresholdSettings.fansCount() != null && thresholdSettings.fansCount().enabled()) {
            boolean meets = document.getAuthorFollowersCount() != null &&
                    document.getAuthorFollowersCount() >= thresholdSettings.fansCount().threshold();
            conditionResults.add(meets);
        }

        // Check read count threshold (using lookingCount)
        if (thresholdSettings.readCount() != null && thresholdSettings.readCount().enabled()) {
            boolean meets = document.getLookingCount() != null &&
                    document.getLookingCount() >= thresholdSettings.readCount().threshold();
            conditionResults.add(meets);
        }

        // Check similar article count threshold
        if (thresholdSettings.similarArticleCount() != null && thresholdSettings.similarArticleCount().enabled()) {
            boolean meets = document.getSimilarityNum() != null &&
                    document.getSimilarityNum() >= thresholdSettings.similarArticleCount().threshold();
            conditionResults.add(meets);
        }

        // If no conditions are set, return true
        if (conditionResults.isEmpty()) {
            return true;
        }

        // Apply condition relation (AND/OR)
        if ("AND".equals(thresholdSettings.conditionRelation())) {
            return conditionResults.stream().allMatch(Boolean::booleanValue);
        } else {
            return conditionResults.stream().anyMatch(Boolean::booleanValue);
        }
    }



    /**
     * Check if value is within threshold range
     */
    private boolean isInRange(LevelSettingsDto.ThresholdRangeDto range, long value) {
        if (range == null) {
            return false;
        }

        boolean aboveMin = range.min() == null || value >= range.min();
        boolean belowMax = range.max() == null || value < range.max();

        return aboveMin && belowMax;
    }

    /**
     * Parse string warning level to enum
     */
    private AlertResult.WarningLevel parseWarningLevel(String levelStr) {
        if (StringUtils.isBlank(levelStr)) {
            return null;
        }

        return switch (levelStr.toUpperCase()) {
            case "SEVERE" -> AlertResult.WarningLevel.SEVERE;
            case "MODERATE" -> AlertResult.WarningLevel.MODERATE;
            case "GENERAL" -> AlertResult.WarningLevel.GENERAL;
            default -> null;
        };
    }

    /**
     * Compare two warning levels and return the higher one
     * Priority: SEVERE > MODERATE > GENERAL > null
     */
    private AlertResult.WarningLevel getHigherLevel(AlertResult.WarningLevel level1, AlertResult.WarningLevel level2) {
        if (level1 == null)
            return level2;
        if (level2 == null)
            return level1;

        // SEVERE has highest priority
        if (level1 == AlertResult.WarningLevel.SEVERE || level2 == AlertResult.WarningLevel.SEVERE) {
            return AlertResult.WarningLevel.SEVERE;
        }

        // MODERATE has second priority
        if (level1 == AlertResult.WarningLevel.MODERATE || level2 == AlertResult.WarningLevel.MODERATE) {
            return AlertResult.WarningLevel.MODERATE;
        }

        // GENERAL has lowest priority
        return AlertResult.WarningLevel.GENERAL;
    }

    /**
     * Determine warning level based on threshold configuration
     */
    private AlertResult.WarningLevel determineThresholdLevel(LevelSettingsDto.LevelThresholdsDto thresholds,
            long value) {
        if (thresholds == null) {
            return null;
        }

        // Check severe threshold first (highest priority)
        if (thresholds.severe() != null && isInRange(thresholds.severe(), value)) {
            return AlertResult.WarningLevel.SEVERE;
        }

        // Check moderate threshold
        if (thresholds.moderate() != null && isInRange(thresholds.moderate(), value)) {
            return AlertResult.WarningLevel.MODERATE;
        }

        // Check general threshold
        if (thresholds.general() != null && isInRange(thresholds.general(), value)) {
            return AlertResult.WarningLevel.GENERAL;
        }

        return null;
    }



    /**
     * Calculate total interactions from document metrics
     */
    private long calculateTotalInteractions(SinaNewsDocument document) {
        long total = 0;
        if (document.getInteractionCount() != null)
            total += document.getInteractionCount();
        return total;
    }

    // JSON parsing helper methods
    private AlertKeywordsDto parseAlertKeywords(String json) {
        // If JSON is null or empty, return null (no keyword filtering)
        if (json == null || json.trim().isEmpty()) {
            log.debug("Alert keywords JSON is null or empty, returning null");
            return null;
        }

        try {
            AlertKeywordsDto result = objectMapper.readValue(json, AlertKeywordsDto.class);
            log.debug("Parsed alert keywords: {}", result);
            return result;
        } catch (Exception e) {
            log.error("Error parsing alert keywords JSON: {}, JSON content: {}", e.getMessage(), json);
            // Return null instead of empty keywords to indicate no keyword filtering
            return null;
        }
    }

    private ContentSettingsDto parseContentSettings(String json) {
        // If JSON is null or empty, return null (no content filtering)
        if (json == null || json.trim().isEmpty()) {
            log.debug("Content settings JSON is null or empty, returning null");
            return null;
        }

        try {
            return objectMapper.readValue(json, ContentSettingsDto.class);
        } catch (Exception e) {
            log.error("Error parsing content settings JSON: {}", e.getMessage());
            // Return default ContentSettingsDto with minimal required fields
            return new ContentSettingsDto(null, null, null, null, null, null, null);
        }
    }

    private ThresholdSettingsDto parseThresholdSettings(String json) {
        // If JSON is null or empty, return null (no threshold filtering)
        if (json == null || json.trim().isEmpty()) {
            log.debug("Threshold settings JSON is null or empty, returning null");
            return null;
        }

        try {
            return objectMapper.readValue(json, ThresholdSettingsDto.class);
        } catch (Exception e) {
            log.error("Error parsing threshold settings JSON: {}", e.getMessage());
            // Return default ThresholdSettingsDto with minimal configuration
            return new ThresholdSettingsDto("OR", null, null, null, null, null);
        }
    }

}
