package com.czb.hn.service.business;

import com.czb.hn.dto.response.search.SearchRequestDto;
import com.czb.hn.dto.response.search.SinaNewsDetailResponseDto;
import com.czb.hn.dto.response.search.SinaNewsSearchResponseDto;

import java.util.List;
import java.util.Map;

public interface ElasticsearchSearchService {

        /**
         * 高级搜索接口，根据多种条件组合查询新浪新闻数据
         *
         * @param requestDto 搜索请求参数
         * @param pageSize    分页大小
         * @param pageNum     分页页码
         * @return 匹配条件的新闻搜索结果列表，封装为 SinaNewsSearchResponseDto 对象
         *
         */
        List<SinaNewsSearchResponseDto> SinaNewsMonitor(
                SearchRequestDto requestDto,
                Integer pageSize,
                Integer pageNum);

        /**
         * 获取媒体来源分布统计
         *
         * @return 媒体来源及其对应的文档数量
         */
        Map<String, Long> advancedSearchMediaDistribution(
                SearchRequestDto requestDto);

        /**
         * 获取信息详情
         *
         * @param contentId 内容ID
         * @param planId    方案ID
         * @return 时间及其对应的文档数量
         */
        SinaNewsDetailResponseDto getNewsDetail(
                        String contentId, Long planId);



        /**
         * 获取信息详情
         *
         * @param contentIds 内容IDs
         * @param planId    方案ID
         * @return 时间及其对应的文档数量
         */
        List<SinaNewsDetailResponseDto> getNewsDetails(
                List<String> contentIds, Long planId);
        /**
         * 统计舆情信息总量
         *
         * @param planId    方案ID
         * @param startTime 监测起始时间（格式：yyyy-MM-dd HH:mm:ss）
         * @param endTime   监测结束时间（格式：yyyy-MM-dd HH:mm:ss）
         * @return 舆情信息总量
         */
        Long countInformationTotal(Long planId, String startTime, String endTime);

        /**
         * 统计舆情敏感信息总量
         *
         * @param planId    方案ID
         * @param startTime 监测起始时间（格式：yyyy-MM-dd HH:mm:ss）
         * @param endTime   监测结束时间（格式：yyyy-MM-dd HH:mm:ss）
         * @return 舆情敏感信息总量
         */
        Long countSensitiveInformationTotal(Long planId, String startTime, String endTime);

        /**
         * 批量统计多个方案的舆情信息总量
         *
         * @param planIds   方案ID列表
         * @param startTime 监测起始时间（格式：yyyy-MM-dd HH:mm:ss）
         * @param endTime   监测结束时间（格式：yyyy-MM-dd HH:mm:ss）
         * @return 各方案的舆情信息总量映射 (planId -> count)
         */
        Map<Long, Long> batchCountInformationTotal(List<Long> planIds, String startTime, String endTime);

        /**
         * 批量统计多个方案的舆情敏感信息总量
         *
         * @param planIds   方案ID列表
         * @param startTime 监测起始时间（格式：yyyy-MM-dd HH:mm:ss）
         * @param endTime   监测结束时间（格式：yyyy-MM-dd HH:mm:ss）
         * @return 各方案的舆情敏感信息总量映射 (planId -> count)
         */
        Map<Long, Long> batchCountSensitiveInformationTotal(List<Long> planIds, String startTime, String endTime);
}
