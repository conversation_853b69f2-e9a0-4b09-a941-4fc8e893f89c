package com.czb.hn.service.business;

import com.czb.hn.document.SinaNewsDocument;
import com.czb.hn.dto.alert.AlertConfigurationResponseDto;

import java.util.List;
import java.util.Map;

/**
 * Keyword Tracking Service Interface
 * Extends SinaNewsMonitor patterns to track which keywords triggered alerts
 * Works with Elasticsearch documents as the primary data source
 */
public interface KeywordTrackingService {
    
    /**
     * Extract involved keywords from Elasticsearch document based on alert configuration
     * 
     * @param config Alert configuration containing keyword rules
     * @param document Elasticsearch document to analyze
     * @return List of keywords that triggered the alert
     */
    Map<String, Integer> extractInvolvedKeywords(AlertConfigurationResponseDto config, SinaNewsDocument document);

}
