package com.czb.hn.service.business.impl;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.json.JsonData;
import com.czb.hn.document.SinaNewsDocument;
import com.czb.hn.dto.alert.AlertConfigurationResponseDto;
import com.czb.hn.dto.alert.AlertResultResponseDto;
import com.czb.hn.enums.ContentCategory;
import com.czb.hn.enums.ContentMatchType;
import com.czb.hn.enums.ContentType;
import com.czb.hn.enums.InformationSensitivityType;
import com.czb.hn.enums.MediaLevel;
import com.czb.hn.enums.SourceType;
import com.czb.hn.jpa.securadar.entity.AlertConfiguration;
import com.czb.hn.jpa.securadar.entity.AlertResult;
import com.czb.hn.jpa.securadar.repository.AlertResultRepository;
import com.czb.hn.service.business.AlertConfigurationConsumerService;
import com.czb.hn.service.business.AlertProcessingService;
import com.czb.hn.service.business.AlertPushService;
import com.czb.hn.service.business.AlertRuleEngine;
import com.czb.hn.service.business.KeywordTrackingService;
import com.czb.hn.util.AlertResultMapper;
import com.czb.hn.util.DateTimeUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Alert Processing Service Implementation
 * Processes Elasticsearch documents against alert configurations
 * Uses existing SinaNewsElasticsearchSearchService patterns
 */
@Service
@Slf4j
public class AlertProcessingServiceImpl implements AlertProcessingService {

    @Autowired
    private ElasticsearchClient elasticsearchClient;

    @Autowired
    private AlertResultRepository alertResultRepository;

    @Autowired
    private AlertConfigurationConsumerService alertConfigConsumerService;

    @Autowired
    private AlertRuleEngine ruleEngine;

    @Autowired
    private KeywordTrackingService keywordTrackingService;

    @Autowired
    private AlertResultMapper alertResultMapper;

    @Autowired
    private AlertPushService alertPushService;

    @Value("${alert.processing.batch-size:100}")
    private int batchSize;

    @Value("${alert.processing.elasticsearch.index:sina_news}")
    private String elasticsearchIndex;

    @Value("${alert.processing.time-window-hours:24}")
    private int timeWindowHours;

    @Override
    @Scheduled(cron = "${alert.processing.cron:0 */3 * * * *}")
    public void processAlerts() {
        try {
            log.info("Starting scheduled alert processing");
            long startTime = System.currentTimeMillis();

            // Get all active enterprises
            List<String> activeEnterprises = getActiveEnterprises();

            // Process each enterprise separately for tenant isolation
            int totalAlerts = activeEnterprises.parallelStream()
                    .mapToInt(enterpriseId -> processEnterpriseAlerts(enterpriseId).size())
                    .sum();

            long processingTime = System.currentTimeMillis() - startTime;
            log.info("Completed scheduled alert processing for {} enterprises, generated {} alerts in {}ms",
                    activeEnterprises.size(), totalAlerts, processingTime);

        } catch (Exception e) {
            log.error("Error in scheduled alert processing: {}", e.getMessage(), e);
        }
    }

    @Override
    public List<AlertResultResponseDto> processEnterpriseAlerts(String enterpriseId) {
        log.info("Processing alerts for enterprise: {}", enterpriseId);

        List<AlertResultResponseDto> results = new ArrayList<>();

        try {
            // Get active configurations for this enterprise
            List<AlertConfigurationResponseDto> configs = alertConfigConsumerService
                    .getActiveConfigurationsByEnterprise(enterpriseId);

            if (configs.isEmpty()) {
                log.debug("No active configurations found for enterprise: {}", enterpriseId);
                return results;
            }

            // Get recent documents from Elasticsearch for processing
            List<SinaNewsDocument> recentDocuments = getRecentDocumentsFromElasticsearch();

            if (recentDocuments.isEmpty()) {
                log.debug("No recent documents found in Elasticsearch for processing");
                return results;
            }

            log.info("Processing {} documents against {} configurations for enterprise: {}",
                    recentDocuments.size(), configs.size(), enterpriseId);

            // Process in batches
            for (int i = 0; i < recentDocuments.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, recentDocuments.size());
                List<SinaNewsDocument> batch = recentDocuments.subList(i, endIndex);

                List<AlertResultResponseDto> batchResults = processBatch(configs, batch);
                results.addAll(batchResults);
            }

            log.info("Generated {} alerts for enterprise: {}", results.size(), enterpriseId);

        } catch (Exception e) {
            log.error("Error processing alerts for enterprise {}: {}", enterpriseId, e.getMessage(), e);
        }

        return results;
    }

    @Override
    public List<AlertResultResponseDto> processPlanAlerts(Long planId) {
        log.info("Processing alerts for plan: {}", planId);

        try {
            // Get active configurations for this plan
            List<AlertConfigurationResponseDto> configs = alertConfigConsumerService
                    .getActiveConfigurationsByPlan(planId);

            if (configs.isEmpty()) {
                log.debug("No active configurations found for plan: {}", planId);
                return new ArrayList<>();
            }

            // Get recent documents from Elasticsearch
            List<SinaNewsDocument> recentDocuments = getRecentDocumentsFromElasticsearch();

            // Process documents against plan configurations
            return processBatch(configs, recentDocuments);

        } catch (Exception e) {
            log.error("Error processing alerts for plan {}: {}", planId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<AlertResultResponseDto> processConfigurationAlerts(Long configurationId) {
        log.info("Processing alerts for configuration: {}", configurationId);

        try {
            // Get specific configuration
            var configOpt = alertConfigConsumerService.getActiveConfigurationById(configurationId);
            if (configOpt.isEmpty()) {
                log.debug("Configuration not found or inactive: {}", configurationId);
                return new ArrayList<>();
            }

            List<AlertConfigurationResponseDto> configs = List.of(configOpt.get());

            // Get recent documents from Elasticsearch
            List<SinaNewsDocument> recentDocuments = getRecentDocumentsFromElasticsearch();

            // Process documents against this configuration
            return processBatch(configs, recentDocuments);

        } catch (Exception e) {
            log.error("Error processing alerts for configuration {}: {}", configurationId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<String> getActiveEnterprises() {
        try {
            // Get all active configurations and extract unique enterprise IDs
            return alertConfigConsumerService.getAllActiveConfigurations()
                    .stream()
                    .map(AlertConfigurationResponseDto::enterpriseId)
                    .distinct()
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("Error getting active enterprises: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public ProcessingStatistics getProcessingStatistics(String enterpriseId) {
        // Implementation for getting processing statistics
        // This would typically query processing logs and metrics
        return new ProcessingStatistics(
                enterpriseId, 0, 0, 0, 0L, "NOT_IMPLEMENTED");
    }

    /**
     * Get recent documents from Elasticsearch for processing
     * Uses time window to limit processing scope
     */
    private List<SinaNewsDocument> getRecentDocumentsFromElasticsearch() {
        try {
            LocalDateTime cutoffTime = LocalDateTime.now().minusHours(timeWindowHours);

            // Build query for recent documents
            BoolQuery.Builder boolQuery = new BoolQuery.Builder()
                    .filter(f -> f.range(r -> r
                            .field("publishTime")
                            .gte(JsonData.of(DateTimeUtil.formatToStandardString(cutoffTime)))));

            Query query = boolQuery.build()._toQuery();
            log.info("Elasticsearch Query: {}", query);

            SearchRequest searchRequest = SearchRequest.of(s -> s
                    .index(elasticsearchIndex)
                    .query(query)
                    .size(10000) // Adjust based on your needs
                    .sort(sort -> sort.field(f -> f.field("publishTime")
                            .order(co.elastic.clients.elasticsearch._types.SortOrder.Desc))));

            SearchResponse<SinaNewsDocument> response = elasticsearchClient.search(searchRequest,
                    SinaNewsDocument.class);

            return response.hits().hits().stream()
                    .map(Hit::source)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("Error fetching recent documents from Elasticsearch: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * Process a batch of documents against configurations
     */
    private List<AlertResultResponseDto> processBatch(
            List<AlertConfigurationResponseDto> configs,
            List<SinaNewsDocument> documents) {

        List<AlertResultResponseDto> results = new ArrayList<>();

        for (SinaNewsDocument document : documents) {
            for (AlertConfigurationResponseDto config : configs) {
                try {
                    // Check if alert already exists to prevent duplicates
                    if (alertResultRepository.existsByEnterpriseIdAndOriginalContentIdAndConfigurationId(
                            config.enterpriseId(), document.getContentId(), config.id())) {
                        continue;
                    }

                    // Convert DTO to entity for rule evaluation
                    AlertConfiguration configEntity = alertResultMapper.toEntity(config);

                    // Evaluate rule against Elasticsearch document
                    if (ruleEngine.evaluateRule(configEntity, document)) {

                        // Create alert result
                        AlertResult alertResult = createAlertResult(config, document);
                        AlertResult saved = alertResultRepository.save(alertResult);

                        // Note: Push notifications are now handled by AlertNotificationScheduler
                        // in a decoupled manner based on reception settings

                        results.add(alertResultMapper.toResponseDto(saved));

                        log.debug("Created alert {} for document {} using configuration {}",
                                saved.getId(), document.getContentId(), config.id());
                    }

                } catch (Exception e) {
                    log.error("Error processing document {} with configuration {}: {}",
                            document.getContentId(), config.id(), e.getMessage(), e);
                }
            }
        }

        return results;
    }

    /**
     * Create AlertResult from configuration and Elasticsearch document
     */
    private AlertResult createAlertResult(AlertConfigurationResponseDto config, SinaNewsDocument document) {
        // Track involved keywords using existing patterns
        // 重新计算关键字词语数
        Map<String, Integer> keywordsCount = keywordTrackingService.extractInvolvedKeywords(config, document);

        // Determine warning level
        AlertResult.WarningLevel warningLevel = ruleEngine.determineWarningLevel(config.levelSettings(), document);

        // 推断内容匹配类型
        ContentMatchType contentMatchType = ruleEngine.extractContentMatchType(config);

        // 根据推断内容类型和命中关键词补充高亮标签
        HighlightedContent highlightedContent = applyKeywordHighlighting(
                document, keywordsCount.keySet(), contentMatchType);

        return AlertResult.builder()
                .enterpriseId(config.enterpriseId())
                .planId(config.planId())
                .configurationId(config.id())
                .originUrl(document.getUrl())
                .title(highlightedContent.getTitle())
                .content(highlightedContent.getContent())
                .involvedKeywords(serializeInvolvedKeywords(keywordsCount))
                .informationSensitivityType(InformationSensitivityType.fromInteger(document.getSensitivityType()))
                .sourceType(SourceType.fromString(document.getMediaType()))
                .contentType(ContentType.fromInteger(document.getContentTypes()))
                .contentMatchType(contentMatchType)
                .contentCategory(ContentCategory.fromInteger(document.getIsOriginal()))
                .mediaLevel(MediaLevel.fromChineseValue(document.getMediaLevel()))
                .warningLevel(warningLevel)
                .source(StringUtils.isBlank(document.getCaptureWebsite()) ? "" : document.getCaptureWebsite())
                .provincial(StringUtils.isBlank(document.getPublishProvince()) ? "" : document.getPublishProvince())
                .publishTime(document.getPublishTime())
                .warningTime(LocalDateTime.now())
                .similarArticleCount(document.getSimilarityNum() != null ? document.getSimilarityNum().intValue() : 0)
                .originalContentId(document.getContentId())
                .processingVersion("1.0")
                .ruleSnapshot(createRuleSnapshot(config))
                .createdBy("SYSTEM")
                .build();
    }

    /**
     * Serialize involved keywords to JSON
     */
    private String serializeInvolvedKeywords(Map<String, Integer> keywords) {
        try {
            return alertResultMapper.getObjectMapper().writeValueAsString(keywords);
        } catch (Exception e) {
            log.error("Error serializing involved keywords: {}", e.getMessage());
            return "[]";
        }
    }

    /**
     * Create rule snapshot for audit purposes
     */
    private String createRuleSnapshot(AlertConfigurationResponseDto config) {
        try {
            return alertResultMapper.getObjectMapper().writeValueAsString(config);
        } catch (Exception e) {
            log.error("Error creating rule snapshot: {}", e.getMessage());
            return "{}";
        }
    }

    /**
     * Apply keyword highlighting based on content match type and involved keywords
     *
     * @param document         The Elasticsearch document
     * @param keywords         Set of keywords that triggered the alert
     * @param contentMatchType The content match type (TITLE, MAIN_TXT, FULL_TEXT)
     * @return HighlightedContent with highlighted title and content
     */
    private HighlightedContent applyKeywordHighlighting(SinaNewsDocument document,
            Set<String> keywords,
            ContentMatchType contentMatchType) {
        String originalTitle = StringUtils.isBlank(document.getTitle()) ? "" : document.getTitle();
        String originalContent = StringUtils.isBlank(document.getContent()) ? "" : document.getContent();

        String highlightedTitle = originalTitle;
        String highlightedContent = originalContent;

        if (keywords.isEmpty()) {
            return new HighlightedContent(highlightedTitle, highlightedContent);
        }

        switch (contentMatchType) {
            case TITLE:
                // 标题中命中 - 只在标题中设置高亮
                highlightedTitle = highlightKeywordsInText(originalTitle, keywords);
                break;

            case MAIN_TXT:
                // 内容中命中 - 只在内容中设置高亮，并提取关键词最早出现的句子
                highlightedContent = highlightKeywordsInText(originalContent, keywords);
                highlightedContent = extractEarliestKeywordSentence(highlightedContent, keywords);
                break;

            case FULL_TEXT:
            default:
                // 全文命中 - 在标题和内容中都设置高亮
                highlightedTitle = highlightKeywordsInText(originalTitle, keywords);
                highlightedContent = highlightKeywordsInText(originalContent, keywords);
                break;
        }

        return new HighlightedContent(highlightedTitle, highlightedContent);
    }

    /**
     * Highlight keywords in text using <strong> tags
     */
    private String highlightKeywordsInText(String text, Set<String> keywords) {
        if (StringUtils.isBlank(text) || keywords.isEmpty()) {
            return text;
        }

        String result = text;
        for (String keyword : keywords) {
            if (StringUtils.isNotBlank(keyword)) {
                // Case-insensitive replacement while preserving original case
                String regex = "(?i)" + Pattern.quote(keyword);
                result = result.replaceAll(regex, "<strong>$0</strong>");
            }
        }

        return result;
    }

    /**
     * Extract the earliest sentence containing keywords from content
     * 内容中设置关键词最早的出现句子，设置进去，之前的句子内容省略
     */
    private String extractEarliestKeywordSentence(String content, Set<String> keywords) {
        if (StringUtils.isBlank(content) || keywords.isEmpty()) {
            return content;
        }

        // Find the earliest keyword position
        int earliestPosition = Integer.MAX_VALUE;
        for (String keyword : keywords) {
            if (StringUtils.isNotBlank(keyword)) {
                int position = content.toLowerCase().indexOf(keyword.toLowerCase());
                if (position != -1 && position < earliestPosition) {
                    earliestPosition = position;
                }
            }
        }

        if (earliestPosition == Integer.MAX_VALUE) {
            return content; // No keywords found
        }

        // Find sentence boundaries around the keyword
        String[] sentences = content.split("[。！？.!?]");
        int currentPosition = 0;

        for (String sentence : sentences) {
            int sentenceEnd = currentPosition + sentence.length();
            if (earliestPosition >= currentPosition && earliestPosition < sentenceEnd) {
                // Found the sentence containing the earliest keyword
                String targetSentence = sentence.trim();
                if (currentPosition > 0) {
                    // Add ellipsis if there's content before this sentence
                    return "..." + targetSentence;
                } else {
                    return targetSentence;
                }
            }
            currentPosition = sentenceEnd + 1; // +1 for the delimiter
        }

        return content; // Fallback
    }

    /**
     * Data class to hold highlighted title and content
     */
    @Data
    private static class HighlightedContent {
        private final String title;
        private final String content;

        public HighlightedContent(String title, String content) {
            this.title = title;
            this.content = content;
        }
    }
}
