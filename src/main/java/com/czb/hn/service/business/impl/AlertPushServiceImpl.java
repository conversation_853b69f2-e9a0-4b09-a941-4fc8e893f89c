package com.czb.hn.service.business.impl;

import com.czb.hn.dto.alert.AlertPushDetailResponseDto;
import com.czb.hn.dto.alert.AlertPushDetailSearchResultDto;
import com.czb.hn.jpa.securadar.entity.AlertPushDetail;
import com.czb.hn.jpa.securadar.entity.AlertResult;
import com.czb.hn.enums.PushStatus;
import com.czb.hn.enums.PushType;
import com.czb.hn.jpa.securadar.repository.AlertPushDetailRepository;
import com.czb.hn.jpa.securadar.repository.AlertResultRepository;
import com.czb.hn.service.business.AlertPushService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import javax.mail.internet.MimeMultipart;

/**
 * Alert Push Service Implementation
 * Manages alert notification push operations with multi-tenant support
 */
@Service
@Slf4j
@Transactional
public class AlertPushServiceImpl implements AlertPushService {

    @Value("${alert.message.sms.template.id}")
    private Long smsTemplateId;

    @Autowired
    private AlertPushDetailRepository alertPushDetailRepository;

    @Autowired
    private AlertResultRepository alertResultRepository;

    @Autowired
    private com.czb.hn.util.SmsUtil smsUtil;

    @Autowired
    private com.czb.hn.util.EmailUtil emailUtil;


    @Override
    @Transactional(readOnly = true)
    public AlertPushDetailSearchResultDto getPushDetailsByAlertIdWithPagination(
            Long alertId, Integer page, Integer size, String sortBy, String sortDirection) {
        log.debug("Getting paginated push details for alert ID: {} with page: {}, size: {}", alertId, page, size);

        try {
            // 设置默认值
            int actualPage = (page != null && page > 0) ? page : 1;
            int actualSize = (size != null && size > 0) ? Math.min(size, 100) : 20; // 限制最大100条
            String actualSortBy = (sortBy != null && !sortBy.trim().isEmpty()) ? sortBy : "pushTime";
            String actualSortDirection = (sortDirection != null && !sortDirection.trim().isEmpty()) ? sortDirection
                    : "desc";

            // 验证排序字段
            if (!isValidSortField(actualSortBy)) {
                log.warn("Invalid sort field: {}, using default: pushTime", actualSortBy);
                actualSortBy = "pushTime";
            }

            // 验证排序方向
            if (!actualSortDirection.equalsIgnoreCase("asc") && !actualSortDirection.equalsIgnoreCase("desc")) {
                log.warn("Invalid sort direction: {}, using default: desc", actualSortDirection);
                actualSortDirection = "desc";
            }

            // 查询AlertResult获取相关信息
            AlertResult alertResult = alertResultRepository.findById(alertId)
                    .orElseThrow(() -> new IllegalArgumentException("Alert not found with ID: " + alertId));

            // 创建分页和排序参数
            Sort.Direction direction = actualSortDirection.equalsIgnoreCase("asc") ? Sort.Direction.ASC
                    : Sort.Direction.DESC;
            Pageable pageable = PageRequest.of(actualPage-1, actualSize, Sort.by(direction, actualSortBy));

            Long alertNotificationQueueId = alertResult.getAlertNotificationQueueId();

            // 使用现有的findWithFilters方法进行分页查询
            Page<AlertPushDetail> pushDetailPage = alertPushDetailRepository.findByAlertNotificationQueueId(alertNotificationQueueId, pageable);

            // 转换为DTO列表
            List<AlertPushDetailResponseDto> pushDetailDtos = pushDetailPage.getContent().stream()
                    .map(this::mapToResponseDto)
                    .collect(Collectors.toList());

            // 构建分页结果
            return new AlertPushDetailSearchResultDto(
                    pushDetailDtos,
                    pushDetailPage.getTotalElements(),
                    pushDetailPage.getTotalPages(),
                    pushDetailPage.getNumber(),
                    pushDetailPage.getSize(),
                    pushDetailPage.isFirst(),
                    pushDetailPage.isLast(),
                    pushDetailPage.hasNext(),
                    pushDetailPage.hasPrevious());

        } catch (Exception e) {
            log.error("Failed to get paginated push details for alert ID: {}", alertId, e);
            throw new RuntimeException("Failed to get paginated push details: " + e.getMessage(), e);
        }
    }

    /**
     * 验证排序字段是否有效
     */
    private boolean isValidSortField(String sortBy) {
        return sortBy != null && (sortBy.equals("pushTime") ||
                sortBy.equals("createdAt") ||
                sortBy.equals("updatedAt") ||
                sortBy.equals("retryCount"));
    }

    /**
     * Map AlertPushDetail entity to response DTO
     */
    private AlertPushDetailResponseDto mapToResponseDto(AlertPushDetail pushDetail) {
        return new AlertPushDetailResponseDto(
                pushDetail.getId(),
                pushDetail.getEnterpriseId(),
                pushDetail.getAccountInfo(),
                pushDetail.getPushType(),
                pushDetail.getPushStatus(),
                pushDetail.getPushTime(),
                pushDetail.getErrorMessage(),
                pushDetail.getPushDetails(),
                pushDetail.getRetryCount(),
                pushDetail.getLastRetryTime(),
                pushDetail.getCreatedAt(),
                pushDetail.getUpdatedAt(),
                pushDetail.getCreatedBy());
    }

    @Override
    public CompletableFuture<Boolean> sendEmailNotification(String email, String subject, String content) {
        log.info("Sending email notification to: {}", email);

        return CompletableFuture.supplyAsync(() -> {
            try {
                emailUtil.sendEmail(email, subject, content);
                // This is a placeholder implementation
                log.info("Email notification sent successfully to: {}", email);
                return true;
            } catch (Exception e) {
                log.error("Failed to send email to: {}", email, e);
                return false;
            }
        });
    }

    @Override
    public CompletableFuture<Boolean> sendSmsNotification(String phoneNumber, String message) {
        log.info("Sending SMS notification to: {}", phoneNumber);

        return CompletableFuture.supplyAsync(() -> {
            try {
                HashMap<String, String> messageMap = new HashMap<>();
                messageMap.put("message", message);
                smsUtil.sendSms(phoneNumber, smsTemplateId, messageMap);
                // This is a placeholder implementation
                log.info("SMS notification sent successfully to: {}", phoneNumber);
                // For now, always return success
                return true;
            } catch (Exception e) {
                log.error("Failed to send SMS to: {}", phoneNumber, e);
                return false;
            }
        });
    }

    @Override
    public CompletableFuture<Boolean> sendComplexEmailNotification(String email, String subject,
            MimeMultipart multipart) {
        log.info("Sending complex email notification to: {}", email);

        return CompletableFuture.supplyAsync(() -> {
            try {
                emailUtil.sendComplexEmail(email, subject, multipart);
                log.info("Complex email notification sent successfully to: {}", email);
                return true;
            } catch (Exception e) {
                log.error("Failed to send complex email to: {}", email, e);
                return false;
            }
        });
    }

    @Override
    public void createPushRecordsForAlert(AlertResult alert, Object recipient) {
        log.debug("Creating push records for alert {} and recipient", alert != null ? alert.getId() : "null");

        try {
            // Cast recipient to RecipientInfo
            if (!(recipient instanceof com.czb.hn.service.business.ReceptionRulesEngine.RecipientInfo recipientInfo)) {
                log.error("Invalid recipient type: {}", recipient.getClass().getName());
                throw new IllegalArgumentException("Recipient must be of type RecipientInfo");
            }

            // Validate alert
            if (alert == null) {
                log.error("Invalid alert: alert is null");
                throw new IllegalArgumentException("Alert cannot be null");
            }
            if (alert.getId() == null) {
                log.error("Invalid alert: alert ID is null");
                throw new IllegalArgumentException("Alert ID cannot be null");
            }

            // Validate recipient has enabled methods
            if (!recipientInfo.hasEnabledMethods()) {
                log.debug("Recipient {} has no enabled notification methods, skipping push record creation",
                        recipientInfo.name());
                return;
            }

            LocalDateTime pushTime = LocalDateTime.now();
            String createdBy = "SYSTEM"; // System-generated push records

            // Create push record for email if enabled
            if (recipientInfo.emailEnabled() && recipientInfo.email() != null && !recipientInfo.email().isBlank()) {
                createPushDetailRecord(alert, recipientInfo, PushType.EMAIL, recipientInfo.email(), pushTime,
                        createdBy);
            }

            // Create push record for SMS if enabled
            if (recipientInfo.smsEnabled() && recipientInfo.phone() != null && !recipientInfo.phone().isBlank()) {
                createPushDetailRecord(alert, recipientInfo, PushType.SMS, recipientInfo.phone(), pushTime, createdBy);
            }

            log.debug("Successfully created push records for alert {} and recipient {}",
                    alert.getId(), recipientInfo.name());

        } catch (Exception e) {
            log.error("Failed to create push records for alert {} and recipient: {}",
                    alert != null ? alert.getId() : "null", e.getMessage(), e);
            throw new RuntimeException("Failed to create push records: " + e.getMessage(), e);
        }
    }

    /**
     * Create a single push detail record
     */
    private void createPushDetailRecord(AlertResult alert,
            com.czb.hn.service.business.ReceptionRulesEngine.RecipientInfo recipientInfo,
            PushType pushType,
            String accountInfo,
            LocalDateTime pushTime,
            String createdBy) {
        try {
            AlertPushDetail pushDetail = AlertPushDetail.builder()
                    .planId(alert.getPlanId())
                    .alertConfigSnapshotId(alert.getConfigurationId()) // Use configurationId as snapshot ID
                    .enterpriseId(alert.getEnterpriseId())
                    .accountInfo(accountInfo)
                    .pushType(pushType)
                    .pushStatus(PushStatus.SUCCESS) // Assume batch push was successful
                    .pushTime(pushTime)
                    .errorMessage(null)
                    .pushDetails(String.format("批量预警推送记录 - %s: %s",
                            pushType.getDescription(), recipientInfo.name()))
                    .retryCount(0)
                    .alertNotificationQueueId(alert.getAlertNotificationQueueId())
                    .createdBy(createdBy)
                    .build();

            AlertPushDetail saved = alertPushDetailRepository.save(pushDetail);

            log.debug("Created push detail record {} for alert {} with type {} to {}",
                    saved.getId(), alert.getId(), pushType, accountInfo);

        } catch (Exception e) {
            log.error("Failed to create push detail record for alert {} with type {} to {}: {}",
                    alert.getId(), pushType, accountInfo, e.getMessage(), e);
            throw new RuntimeException("Failed to create push detail record: " + e.getMessage(), e);
        }
    }

    @Override
    public void createNoAlertPushRecord(Object notification, Object recipient) {
        log.debug("Creating push record for no-alert notification");

        try {
            // Cast notification to AlertNotificationQueue
            if (!(notification instanceof com.czb.hn.jpa.securadar.entity.AlertNotificationQueue notificationQueue)) {
                log.error("Invalid notification type: {}", notification.getClass().getName());
                throw new IllegalArgumentException("Notification must be of type AlertNotificationQueue");
            }

            // Cast recipient to RecipientInfo
            if (!(recipient instanceof com.czb.hn.service.business.ReceptionRulesEngine.RecipientInfo recipientInfo)) {
                log.error("Invalid recipient type: {}", recipient.getClass().getName());
                throw new IllegalArgumentException("Recipient must be of type RecipientInfo");
            }

            // Validate recipient has enabled methods
            if (!recipientInfo.hasEnabledMethods()) {
                log.debug("Recipient {} has no enabled notification methods, skipping push record creation",
                        recipientInfo.name());
                return;
            }

            LocalDateTime pushTime = LocalDateTime.now();
            String createdBy = "SYSTEM"; // System-generated push records

            // Create push record for email if enabled
            if (recipientInfo.emailEnabled() && recipientInfo.email() != null && !recipientInfo.email().isBlank()) {
                createNoAlertPushDetailRecord(notificationQueue, recipientInfo, PushType.EMAIL,
                        recipientInfo.email(), pushTime, createdBy);
            }

            // Create push record for SMS if enabled
            if (recipientInfo.smsEnabled() && recipientInfo.phone() != null && !recipientInfo.phone().isBlank()) {
                createNoAlertPushDetailRecord(notificationQueue, recipientInfo, PushType.SMS,
                        recipientInfo.phone(), pushTime, createdBy);
            }

            log.debug("Successfully created no-alert push records for notification {} and recipient {}",
                    notificationQueue.getId(), recipientInfo.name());

        } catch (Exception e) {
            log.error("Failed to create no-alert push records: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to create no-alert push records: " + e.getMessage(), e);
        }
    }

    /**
     * Create a single push detail record for no-alert notification
     */
    private void createNoAlertPushDetailRecord(
            com.czb.hn.jpa.securadar.entity.AlertNotificationQueue notificationQueue,
            com.czb.hn.service.business.ReceptionRulesEngine.RecipientInfo recipientInfo,
            PushType pushType,
            String accountInfo,
            LocalDateTime pushTime,
            String createdBy) {
        try {
            AlertPushDetail pushDetail = AlertPushDetail.builder()
                    .planId(notificationQueue.getPlanId())
                    .alertConfigSnapshotId(notificationQueue.getConfigurationId())
                    .enterpriseId(notificationQueue.getEnterpriseId())
                    .accountInfo(accountInfo)
                    .pushType(pushType)
                    .pushStatus(PushStatus.SUCCESS) // Assume no-alert push was successful
                    .pushTime(pushTime)
                    .errorMessage(null)
                    .pushDetails(String.format("无预警通知推送记录 - %s: %s",
                            pushType.getDescription(), recipientInfo.name()))
                    .retryCount(0)
                    .createdBy(createdBy)
                    .build();

            AlertPushDetail saved = alertPushDetailRepository.save(pushDetail);

            log.debug("Created no-alert push detail record {} for notification {} with type {} to {}",
                    saved.getId(), notificationQueue.getId(), pushType, accountInfo);

        } catch (Exception e) {
            log.error("Failed to create no-alert push detail record for notification {} with type {} to {}: {}",
                    notificationQueue.getId(), pushType, accountInfo, e.getMessage(), e);
            throw new RuntimeException("Failed to create no-alert push detail record: " + e.getMessage(), e);
        }
    }

}
