package com.czb.hn.service.business.impl;

import com.czb.hn.document.SinaNewsDocument;
import com.czb.hn.dto.alert.AlertConfigurationResponseDto;
import com.czb.hn.dto.alert.config.AlertKeywordsDto;
import com.czb.hn.dto.alert.config.ContentSettingsDto;
import com.czb.hn.enums.ContentMatchType;
import com.czb.hn.service.business.KeywordTrackingService;
import lombok.extern.slf4j.Slf4j;
import org.ahocorasick.trie.Emit;
import org.ahocorasick.trie.Trie;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Keyword Tracking Service Implementation
 * Extends SinaNewsMonitor patterns for alert processing
 * Works with Elasticsearch documents as the primary data source
 */
@Service
@Slf4j
public class KeywordTrackingServiceImpl implements KeywordTrackingService {

    @Override
    public Map<String, Integer> extractInvolvedKeywords(AlertConfigurationResponseDto config, SinaNewsDocument document) {
        Map<String, Integer> countMap = new HashMap<>();
        if (config == null) {
            return countMap;
        }
        AlertKeywordsDto alertKeywordsDto = config.alertKeywords();
        ContentSettingsDto contentSettingsDto = config.contentSettings();


        List<String> keywords = alertKeywordsDto.keywords();

        if (CollectionUtils.isEmpty(keywords)) {
            return countMap;
        }

        // 构造文本
        String content = buildSearchableContent(document, contentSettingsDto);

        Trie trie = Trie.builder()
                .addKeywords(keywords)
                .build();
        Collection<Emit> emits = trie.parseText(content);

        for (Emit emit : emits) {
            String keyword = emit.getKeyword();
            countMap.put(keyword, countMap.getOrDefault(keyword, 0) + 1);
        }

        return countMap;
    }


    /**
     * Build searchable content from Elasticsearch document
     * Similar to existing SinaNewsMonitor patterns
     */
    private String buildSearchableContent(SinaNewsDocument document, ContentSettingsDto contentSettingsDto) {
        StringBuilder content = new StringBuilder();

        if (contentSettingsDto != null) {
            Integer matchMethod = contentSettingsDto.matchMethod();
            ContentMatchType contentMatchType = ContentMatchType.fromInteger(matchMethod);
            if (ContentMatchType.TITLE.equals(contentMatchType)) {
                if (StringUtils.isNotBlank(document.getTitle())) {
                    content.append(document.getTitle());
                }
            } else if (ContentMatchType.MAIN_TXT.equals(contentMatchType)) {
                if (StringUtils.isNotBlank(document.getContent())) {
                    content.append(document.getContent());
                }
            } else {
                if (StringUtils.isNotBlank(document.getTitle())) {
                    content.append(document.getTitle()).append(" ");
                }
                if (StringUtils.isNotBlank(document.getContent())) {
                    content.append(document.getContent());
                }
            }
        } else {
            if (StringUtils.isNotBlank(document.getTitle())) {
                content.append(document.getTitle()).append(" ");
            }
            if (StringUtils.isNotBlank(document.getContent())) {
                content.append(document.getContent());
            }
        }

        return content.toString();
    }

}
