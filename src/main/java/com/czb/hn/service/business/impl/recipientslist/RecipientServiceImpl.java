package com.czb.hn.service.business.impl.recipientslist;


import com.czb.hn.dto.briefing.BriefingConfigurationResponseDto;
import com.czb.hn.dto.briefing.config.BriefingReceptionSettingsDto;
import com.czb.hn.dto.recipients.*;
import com.czb.hn.enums.PushSystemType;
import com.czb.hn.enums.PushType;
import com.czb.hn.jpa.securadar.entity.Recipients;
import com.czb.hn.jpa.securadar.repository.RecipientsListRepository;
import com.czb.hn.service.business.BriefingConfigurationService;
import com.czb.hn.service.business.ReceptionRulesEngine;
import com.czb.hn.service.business.RecipientService;
import com.czb.hn.util.RecipientsListMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Transactional
public class RecipientServiceImpl implements RecipientService {
    private static final Logger logger = LoggerFactory.getLogger(RecipientServiceImpl.class);

    @Autowired
    private RecipientsListRepository recipientsListRepository;

    @Autowired
    private RecipientsListMapper recipientsListMapper;

    @Autowired
    private ReceptionRulesEngine receptionRulesEngine;

    @Autowired
    private BriefingConfigurationService  briefingConfigurationService;


    @Override
    public List<RecipientsResponseDto> getRecipientsList(RecipientsRequireDto requireDto) {
        try {
            logger.info("Retrieving recipients list by plan id: {}, system type: {}, receive type: {}",
                    requireDto.planId(), requireDto.systemType(), requireDto.receiveType());
            List<Recipients> recipients = recipientsListRepository.findByPlanIdAndSystemTypeAndReceiveType(
                    requireDto.planId(), requireDto.systemType(), PushType.fromString(requireDto.receiveType()));
            logger.info("Successfully retrieved {} recipients list", recipients.size());
            return recipientsListMapper.toResponseDto(recipients);
        } catch (Exception e) {
            logger.error("Error retrieving recipients list by plan id {}: {}", requireDto.planId(), e.getMessage(), e);
            throw new RuntimeException("Failed to retrieve recipients list: " + e.getMessage(), e);
        }
    }

    @Override
    public Boolean createRecipients(RecipientsCreateDto createDto) {
        try {
            logger.info("Creating recipients list: {}", createDto);
            Recipients createRecipientsList = recipientsListMapper.toCreateEntity(createDto);
            Recipients savedRecipientsList = recipientsListRepository.save(createRecipientsList);
            logger.info("Successfully created recipients list: {}", savedRecipientsList);
            return true;
        } catch (Exception e) {
            logger.error("Error creating recipients list: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to create recipients list: " + e.getMessage(), e);
        }
    }

    @Override
    public Boolean deleteRecipients(Long id) {
        try {
            recipientsListRepository.deleteById(id);
            logger.info("Successfully deleted recipients list");
            return true;
        } catch (Exception e) {
            logger.error("Error deleting recipients list: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to delete recipients list", e);
        }
    }

    @Override
    public Boolean enable(Long id, boolean enable) {
        Optional<Recipients> recipientsOp = recipientsListRepository.findById(id);
        if (recipientsOp.isEmpty()) {
            return false;
        }
        Recipients recipients = recipientsOp.get();
        recipients.setEnable(enable);
        recipientsListRepository.save(recipients);
        return true;
    }


    @Override
    public List<RecipientsBulletinDto> getRecipientsBulletinList(Long planId, PushSystemType systemType, PushType pushType) {
        BriefingConfigurationResponseDto briffingConfiguration = briefingConfigurationService.getConfigurationsByPlanId(planId);
        if (briffingConfiguration == null) {
            return List.of();
        }
        Boolean active = briffingConfiguration.isActive();
        if (!active) {
            return List.of();
        }
        Boolean dailyBriefingIsActive = briffingConfiguration.dailyBriefingIsActive();
        Boolean weeklyBriefingIsActive = briffingConfiguration.weeklyBriefingIsActive();
        Boolean monthlyBriefingIsActive = briffingConfiguration.monthlyBriefingIsActive();
        Integer systemTypeRe = systemType.getValue();

        List<Recipients> recipients = recipientsListRepository.findByPlanIdAndSystemTypeAndReceiveType(
                planId, systemTypeRe, pushType);


        List<ReceptionRulesEngine.RecipientInfo> recipientInfos = new ArrayList<>();

        if (PushSystemType.DAILY.equals(systemType) && dailyBriefingIsActive) {
            BriefingReceptionSettingsDto briefingReceptionSettingsDto = briffingConfiguration.dailyReceptionSettings();
            ReceptionMethodsDto receptionMethodsDto = briefingReceptionSettingsDto.receptionMethods();
            recipientInfos = receptionRulesEngine.extractRecipients(receptionMethodsDto);
        }

        if (PushSystemType.WEEKLY.equals(systemType) && weeklyBriefingIsActive) {
            BriefingReceptionSettingsDto briefingReceptionSettingsDto = briffingConfiguration.weeklyReceptionSettings();
            ReceptionMethodsDto receptionMethodsDto = briefingReceptionSettingsDto.receptionMethods();
             recipientInfos = receptionRulesEngine.extractRecipients(receptionMethodsDto);
        }

        if (PushSystemType.MONTHLY.equals(systemType) && monthlyBriefingIsActive) {
            BriefingReceptionSettingsDto briefingReceptionSettingsDto = briffingConfiguration.monthlyReceptionSettings();
            ReceptionMethodsDto receptionMethodsDto = briefingReceptionSettingsDto.receptionMethods();
             recipientInfos = receptionRulesEngine.extractRecipients(receptionMethodsDto);
        }

        // 根据邮箱或手机号匹配recipientInfos和recipientsBulletinDtos
        for (Recipients re : recipients) {
            for (ReceptionRulesEngine.RecipientInfo info : recipientInfos) {
                // 优先使用邮箱匹配，其次使用手机号
                if (PushType.SMS.equals(pushType)) {
                    if (info.phone()!=null && re.getAddress()!=null && info.phone().equals(re.getAddress())) {
                        re.setEnable(true);
                    }
                }

                if (PushType.EMAIL.equals(pushType)) {
                    if (info.email()!=null && re.getAddress()!=null && info.email().equals(re.getAddress())) {
                        re.setEnable(true);
                    }
                }
            }
        }
        return recipientsListMapper.toBulletinDtoList(recipients);
    }

    @Override
    public ReceiveInfoBulletinDto getReceiveInfoBulletin(Long planId, PushSystemType systemType, PushType pushType) {
        BriefingConfigurationResponseDto briffingConfiguration = briefingConfigurationService.getConfigurationsByPlanId(planId);
        if (briffingConfiguration == null) {
            return new ReceiveInfoBulletinDto(false, List.of());
        }
    
        boolean enabled = false;
        if (PushSystemType.DAILY.equals(systemType)) {
            enabled = Boolean.TRUE.equals(briffingConfiguration.dailyBriefingIsActive());
        } else if (PushSystemType.WEEKLY.equals(systemType)) {
            enabled = Boolean.TRUE.equals(briffingConfiguration.weeklyBriefingIsActive());
        } else if (PushSystemType.MONTHLY.equals(systemType)) {
            enabled = Boolean.TRUE.equals(briffingConfiguration.monthlyBriefingIsActive());
        }
        
        List<RecipientsBulletinDto> recipients = getRecipientsBulletinList(planId, systemType, pushType);

        return new ReceiveInfoBulletinDto(enabled, recipients);
    }

    @Override
    public List<RecipientsBulletinDto> getRecipientsByIds(List<Long> ids) {
        try {
            logger.info("Retrieving recipients by ids: {}", ids);
            if (ids == null || ids.isEmpty()) {
                logger.warn("Empty id list provided for recipient lookup");
                return List.of();
            }
            
            List<Recipients> recipients = recipientsListRepository.findAllById(ids);
            logger.info("Successfully retrieved {} recipients from {} ids", recipients.size(), ids.size());
            
            // 由于RecipientsBulletinDto是record类型，无法修改字段值
            // 创建新的DTO对象，确保enable字段为true
            return recipients.stream()
                .map(recipient -> new RecipientsBulletinDto(
                    recipient.getId(),
                    recipient.getName(),
                    recipient.getAddress(),
                    true // 强制设置为启用状态
                ))
                .collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("Error retrieving recipients by ids {}: {}", ids, e.getMessage(), e);
            throw new RuntimeException("Failed to retrieve recipients by ids: " + e.getMessage(), e);
        }
    }
}
