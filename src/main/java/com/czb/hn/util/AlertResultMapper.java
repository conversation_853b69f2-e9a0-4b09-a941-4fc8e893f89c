package com.czb.hn.util;

import com.czb.hn.dto.alert.AlertConfigurationResponseDto;
import com.czb.hn.dto.alert.AlertResultResponseDto;
import com.czb.hn.jpa.securadar.entity.AlertConfiguration;
import com.czb.hn.jpa.securadar.entity.AlertResult;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Alert Result Mapper
 * Handles conversion between AlertResult entities and DTOs
 * Provides JSON serialization/deserialization utilities
 */
@Component
@Slf4j
public class AlertResultMapper {

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * Convert AlertResult entity to response DTO
     */
    public AlertResultResponseDto toResponseDto(AlertResult entity) {
        if (entity == null) {
            return null;
        }

        try {
            return new AlertResultResponseDto(
                    entity.getId(),
                    entity.getEnterpriseId(),
                    entity.getPlanId(),
                    entity.getConfigurationId(),
                    entity.getTitle(),
                    entity.getContent(),
                    entity.getOriginUrl(),
                    entity.getInvolvedKeywords(),
                    entity.getInformationSensitivityType() != null ? entity.getInformationSensitivityType().getValue()
                            : null,
                    entity.getContentCategory() != null ? entity.getContentCategory().getValue() : null,
                    entity.getSourceType() != null ? entity.getSourceType().getValue() : null,
                    entity.getContentType() != null ? entity.getContentType().getValue() : null,
                    entity.getContentMatchType() != null ? entity.getContentMatchType().getValue() : null,
                    entity.getMediaLevel() != null ? entity.getMediaLevel().getValue() : null,
                    entity.getWarningLevel() != null ? entity.getWarningLevel().name() : null,
                    entity.getSource(),
                    entity.getProvincial(),
                    entity.getPublishTime(),
                    entity.getWarningTime(),
                    entity.getSimilarArticleCount(),
                    entity.getOriginalContentId(),
                    parseExtendedAttributes(entity.getExtendedAttributes()),
                    entity.getCreatedAt(),
                    entity.getUpdatedAt());
        } catch (Exception e) {
            log.error("Error converting AlertResult entity to DTO: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to convert AlertResult entity to DTO", e);
        }
    }

    /**
     * Convert AlertConfigurationResponseDto to AlertConfiguration entity
     * Used for rule evaluation
     */
    public AlertConfiguration toEntity(AlertConfigurationResponseDto dto) {
        if (dto == null) {
            return null;
        }

        try {
            AlertConfiguration entity = new AlertConfiguration();
            entity.setId(dto.id());
            entity.setName(dto.name());
            entity.setDescription(dto.description());
            entity.setPlanId(dto.planId());
            entity.setEnterpriseId(dto.enterpriseId());
            entity.setEnabled(dto.enabled());

            // Convert DTOs to JSON strings
            entity.setAlertKeywords(objectMapper.writeValueAsString(dto.alertKeywords()));
            entity.setContentSettings(objectMapper.writeValueAsString(dto.contentSettings()));
            entity.setThresholdSettings(objectMapper.writeValueAsString(dto.thresholdSettings()));
            entity.setLevelSettings(objectMapper.writeValueAsString(dto.levelSettings()));
            entity.setReceptionSettings(objectMapper.writeValueAsString(dto.receptionSettings()));

            entity.setCreatedAt(dto.createdAt());
            entity.setUpdatedAt(dto.updatedAt());
            entity.setCreatedBy(dto.createdBy());
            entity.setUpdatedBy(dto.updatedBy());
            entity.setCurrentVersion(dto.currentVersion());
            entity.setIsActive(dto.isActive());
            entity.setLastSnapshotAt(dto.lastSnapshotAt());

            return entity;
        } catch (Exception e) {
            log.error("Error converting AlertConfigurationResponseDto to entity: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to convert AlertConfigurationResponseDto to entity", e);
        }
    }


    /**
     * Parse extended attributes from JSON string
     */
    private Map<String, Object> parseExtendedAttributes(String json) {
        if (json == null || json.trim().isEmpty()) {
            return new HashMap<>();
        }

        try {
            return objectMapper.readValue(json, new TypeReference<Map<String, Object>>() {
            });
        } catch (Exception e) {
            log.warn("Error parsing extended attributes JSON: {}", e.getMessage());
            return new HashMap<>();
        }
    }


    /**
     * Serialize involved keywords with count to JSON string
     * Format: [{"keyword":"关键词1","count":3},{"keyword":"关键词2","count":2}]
     */
    public String serializeInvolvedKeywordsWithCount(Map<String, Integer> keywordCounts) {
        if (keywordCounts == null || keywordCounts.isEmpty()) {
            return "[]";
        }

        try {
            List<Map<String, Object>> keywordList = keywordCounts.entrySet().stream()
                    .map(entry -> {
                        Map<String, Object> item = new HashMap<>();
                        item.put("keyword", entry.getKey());
                        item.put("count", entry.getValue());
                        return item;
                    })
                    .toList();
            return objectMapper.writeValueAsString(keywordList);
        } catch (Exception e) {
            log.error("Error serializing involved keywords with count: {}", e.getMessage(), e);
            return "[]";
        }
    }

    /**
     * Parse involved keywords with count from JSON string
     * Returns Map<String, Integer> where key is keyword and value is count
     */
    public Map<String, Integer> parseInvolvedKeywordsWithCount(String json) {
        if (json == null || json.trim().isEmpty()) {
            return new HashMap<>();
        }

        try {
            List<Map<String, Object>> keywordList = objectMapper.readValue(json,
                    new TypeReference<List<Map<String, Object>>>() {
                    });

            Map<String, Integer> result = new HashMap<>();
            for (Map<String, Object> item : keywordList) {
                String keyword = (String) item.get("keyword");
                Integer count = (Integer) item.get("count");
                if (keyword != null && count != null) {
                    result.put(keyword, count);
                }
            }
            return result;
        } catch (Exception e) {
            log.warn("Error parsing involved keywords with count JSON: {}", e.getMessage());
            return new HashMap<>();
        }
    }


    /**
     * Get ObjectMapper instance for external use
     */
    public ObjectMapper getObjectMapper() {
        return objectMapper;
    }

}
