package com.czb.hn.web.controllers;

import com.czb.hn.constant.CommonConstants;
import com.czb.hn.dto.alert.AlertPushDetailSearchResultDto;
import com.czb.hn.dto.response.ApiResponse;
import com.czb.hn.service.business.AlertPushService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.security.RolesAllowed;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;


/**
 * Alert Push Controller
 * 预警推送管理控制器，提供预警推送记录的查询、管理和统计API
 */
@RestController
@RequestMapping("/alert-push")
@Tag(name = "AlertPushController", description = "预警推送记录查询、管理和统计API")
@Slf4j
public class AlertPushController {

    @Autowired
    private AlertPushService alertPushService;


    /**
     * 根据预警ID分页查询推送详情
     */
    @GetMapping("/details/alert/{alertId}")
    @RolesAllowed(value = { CommonConstants.SYSTEM_ADMIN, CommonConstants.ENTERPRISE_ADMIN,
            CommonConstants.ENTERPRISE_USER })
    @Operation(summary = "根据预警ID分页查询推送详情", description = "获取指定预警的推送详情记录，支持分页、排序")
    public ResponseEntity<ApiResponse<AlertPushDetailSearchResultDto>> getPushDetailsByAlertIdPaginated(
            @Parameter(description = "预警ID") @PathVariable Long alertId,
            @Parameter(description = "页码（从0开始）", example = "1") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小", example = "20") @RequestParam(defaultValue = "20") Integer size,
            @Parameter(description = "排序字段（pushTime, createdAt, updatedAt, retryCount）", example = "pushTime") @RequestParam(defaultValue = "pushTime") String sortBy,
            @Parameter(description = "排序方向（asc, desc）", example = "desc") @RequestParam(defaultValue = "desc") String sortDirection) {

        try {
            log.info(
                    "Getting paginated push details for alert ID: {} with page: {}, size: {}, sortBy: {}, sortDirection: {}",
                    alertId, page, size, sortBy, sortDirection);

            AlertPushDetailSearchResultDto result = alertPushService.getPushDetailsByAlertIdWithPagination(
                    alertId, page, size, sortBy, sortDirection);

            return ResponseEntity.ok(new ApiResponse<>("SUCCESS", "查询成功", result));
        } catch (IllegalArgumentException e) {
            log.warn("Invalid request for alert ID: {}: {}", alertId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(new ApiResponse<>("ERROR", "请求参数错误: " + e.getMessage(), null));
        } catch (Exception e) {
            log.error("Failed to get paginated push details for alert ID: {}", alertId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse<>("ERROR", "查询推送详情失败: " + e.getMessage(), null));
        }
    }

}
