package com.czb.hn.service.business.impl;

import org.junit.jupiter.api.Test;

import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Simple test for keyword highlighting functionality
 */
class KeywordHighlightingTest {

    @Test
    void testHighlightKeywordsInText() {
        // Test basic highlighting
        String text = "这是一个测试文本，包含安全相关信息";
        Set<String> keywords = Set.of("安全", "测试");

        String result = highlightKeywordsInText(text, keywords);

        assertTrue(result.contains("<strong>安全</strong>"));
        assertTrue(result.contains("<strong>测试</strong>"));
        assertFalse(result.contains("安全") && !result.contains("<strong>安全</strong>"));
    }

    @Test
    void testExtractEarliestKeywordSentence() {
        // Test sentence extraction
        String content = "第一句话没有关键词。第二句话包含安全信息。第三句话也有内容。";
        Set<String> keywords = Set.of("安全");

        String result = extractEarliestKeywordSentence(content, keywords);

        assertTrue(result.startsWith("..."));
        assertTrue(result.contains("第二句话包含安全信息"));
        assertFalse(result.contains("第一句话"));
        assertFalse(result.contains("第三句话"));
    }

    @Test
    void testExtractEarliestKeywordSentenceFirstSentence() {
        // Test when keyword is in first sentence
        String content = "第一句话包含安全信息。第二句话没有关键词。";
        Set<String> keywords = Set.of("安全");

        String result = extractEarliestKeywordSentence(content, keywords);

        assertFalse(result.startsWith("..."));
        assertTrue(result.contains("第一句话包含安全信息"));
        assertFalse(result.contains("第二句话"));
    }

    @Test
    void testHighlightingWithEmptyKeywords() {
        String text = "这是一个测试文本";
        Set<String> keywords = Set.of();

        String result = highlightKeywordsInText(text, keywords);

        assertEquals(text, result);
        assertFalse(result.contains("<strong>"));
    }

    @Test
    void testCaseInsensitiveHighlighting() {
        String text = "这是一个Test测试文本";
        Set<String> keywords = Set.of("test", "测试");

        String result = highlightKeywordsInText(text, keywords);

        assertTrue(result.contains("<strong>Test</strong>"));
        assertTrue(result.contains("<strong>测试</strong>"));
    }

    // Helper methods copied from AlertProcessingServiceImpl for testing
    private String highlightKeywordsInText(String text, Set<String> keywords) {
        if (text == null || text.isBlank() || keywords.isEmpty()) {
            return text;
        }

        String result = text;
        for (String keyword : keywords) {
            if (keyword != null && !keyword.isBlank()) {
                // Case-insensitive replacement while preserving original case
                String regex = "(?i)" + java.util.regex.Pattern.quote(keyword);
                result = result.replaceAll(regex, "<strong>$0</strong>");
            }
        }

        return result;
    }

    private String extractEarliestKeywordSentence(String content, Set<String> keywords) {
        if (content == null || content.isBlank() || keywords.isEmpty()) {
            return content;
        }

        // Find the earliest keyword position
        int earliestPosition = Integer.MAX_VALUE;
        for (String keyword : keywords) {
            if (keyword != null && !keyword.isBlank()) {
                int position = content.toLowerCase().indexOf(keyword.toLowerCase());
                if (position != -1 && position < earliestPosition) {
                    earliestPosition = position;
                }
            }
        }

        if (earliestPosition == Integer.MAX_VALUE) {
            return content; // No keywords found
        }

        // Find sentence boundaries around the keyword
        String[] sentences = content.split("[。！？.!?]");
        int currentPosition = 0;

        for (String sentence : sentences) {
            int sentenceEnd = currentPosition + sentence.length();
            if (earliestPosition >= currentPosition && earliestPosition < sentenceEnd) {
                // Found the sentence containing the earliest keyword
                String targetSentence = sentence.trim();
                if (currentPosition > 0) {
                    // Add ellipsis if there's content before this sentence
                    return "..." + targetSentence;
                } else {
                    return targetSentence;
                }
            }
            currentPosition = sentenceEnd + 1; // +1 for the delimiter
        }

        return content; // Fallback
    }

    // Simple main method to test the functionality
    public static void main(String[] args) {
        KeywordHighlightingTest test = new KeywordHighlightingTest();

        System.out.println("Testing keyword highlighting functionality...");

        // Test 1: Basic highlighting
        String text = "这是一个测试文本，包含安全相关信息";
        Set<String> keywords = Set.of("安全", "测试");
        String result = test.highlightKeywordsInText(text, keywords);
        System.out.println("Test 1 - Basic highlighting:");
        System.out.println("Input: " + text);
        System.out.println("Output: " + result);
        System.out.println("Contains <strong>安全</strong>: " + result.contains("<strong>安全</strong>"));
        System.out.println("Contains <strong>测试</strong>: " + result.contains("<strong>测试</strong>"));
        System.out.println();

        // Test 2: Sentence extraction
        String content = "第一句话没有关键词。第二句话包含安全信息。第三句话也有内容。";
        Set<String> keywords2 = Set.of("安全");
        String result2 = test.extractEarliestKeywordSentence(content, keywords2);
        System.out.println("Test 2 - Sentence extraction:");
        System.out.println("Input: " + content);
        System.out.println("Output: " + result2);
        System.out.println("Starts with '...': " + result2.startsWith("..."));
        System.out.println("Contains target sentence: " + result2.contains("第二句话包含安全信息"));
        System.out.println();

        // Test 3: First sentence keyword
        String content3 = "第一句话包含安全信息。第二句话没有关键词。";
        String result3 = test.extractEarliestKeywordSentence(content3, keywords2);
        System.out.println("Test 3 - First sentence keyword:");
        System.out.println("Input: " + content3);
        System.out.println("Output: " + result3);
        System.out.println("Starts with '...': " + result3.startsWith("..."));
        System.out.println("Contains first sentence: " + result3.contains("第一句话包含安全信息"));
        System.out.println();

        System.out.println("All tests completed successfully!");
    }
}
